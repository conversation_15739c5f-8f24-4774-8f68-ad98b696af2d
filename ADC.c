#include "esp_adc_cal.h"
#include "driver/adc.h"
#include "driver/timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "ADC_HARDWARE";

// 定义ADC通道和衰减
#define ADC_UNIT                ADC_UNIT_1       // 使用ADC1
#define ADC_ATTEN               ADC_ATTEN_DB_12  // 0mV ~ 2500mV 衰减范围
#define ADC_WIDTH               ADC_WIDTH_BIT_12 // 12位分辨率
#define VOLTAGE_DIVIDER_OFFSET  1
// 定义默认参考电压（当eFuse数据不可用时回退使用，但对于C3建议设为0）
#define DEFAULT_VREF        1100             // 1100 mV
adc1_channel_t adc1_channel = ADC1_CHANNEL_4;

// 硬件定时器多次采样配置
#define NO_OF_SAMPLES       64               // 多次采样求平均
#define TIMER_GROUP         TIMER_GROUP_0    // 定时器组
#define TIMER_IDX           TIMER_0          // 定时器索引
#define TIMER_FREQ_HZ       1000000          // 定时器频率 1MHz
#define SAMPLE_FREQ_HZ      20000            // 采样频率 20kHz
#define TIMER_INTERVAL_US   (1000000 / SAMPLE_FREQ_HZ)  // 定时器间隔

// 全局变量
static esp_adc_cal_characteristics_t adc_chars;
static QueueHandle_t adc_queue = NULL;
static TaskHandle_t adc_task_handle = NULL;
static bool adc_initialized = false;
static uint32_t adc_samples[NO_OF_SAMPLES];
static volatile int sample_count = 0;
static volatile bool sampling_complete = false;

// 定时器中断服务程序
static bool IRAM_ATTR timer_isr_callback(void *args)
{
    BaseType_t high_task_awoken = pdFALSE;

    if (sample_count < NO_OF_SAMPLES) {
        // 读取ADC原始值
        adc_samples[sample_count] = adc1_get_raw(adc1_channel);
        sample_count++;

        if (sample_count >= NO_OF_SAMPLES) {
            sampling_complete = true;
            // 通知任务采样完成
            vTaskNotifyGiveFromISR(adc_task_handle, &high_task_awoken);
        }
    }

    return high_task_awoken == pdTRUE;
}

// ADC硬件初始化函数
esp_err_t adc_hardware_init(void)
{
    if (adc_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Initializing ADC hardware sampling...");

    // 配置ADC
    adc1_config_width(ADC_WIDTH);
    adc1_config_channel_atten(adc1_channel, ADC_ATTEN);

    // 初始化ADC校准
    esp_adc_cal_value_t cal_type = esp_adc_cal_characterize(
        ADC_UNIT, ADC_ATTEN, ADC_WIDTH, DEFAULT_VREF, &adc_chars);

    if (cal_type == ESP_ADC_CAL_VAL_EFUSE_TP) {
        ESP_LOGI(TAG, "Two Point calibration used");
    } else if (cal_type == ESP_ADC_CAL_VAL_EFUSE_VREF) {
        ESP_LOGI(TAG, "eFuse Vref calibration used, vref = %d mV", adc_chars.vref);
    } else {
        ESP_LOGI(TAG, "Default Vref used, vref = %d mV", adc_chars.vref);
    }

    // 配置定时器
    timer_config_t timer_config = {
        .divider = TIMER_FREQ_HZ / 1000000,  // 1MHz时钟
        .counter_dir = TIMER_COUNT_UP,
        .counter_en = TIMER_PAUSE,
        .alarm_en = TIMER_ALARM_EN,
        .auto_reload = true,
    }

    esp_err_t ret = timer_init(TIMER_GROUP, TIMER_IDX, &timer_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Timer init failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 设置定时器报警值
    timer_set_alarm_value(TIMER_GROUP, TIMER_IDX, TIMER_INTERVAL_US);
    timer_enable_intr(TIMER_GROUP, TIMER_IDX);

    // 注册定时器中断回调
    ret = timer_isr_callback_add(TIMER_GROUP, TIMER_IDX, timer_isr_callback, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Timer ISR callback add failed: %s", esp_err_to_name(ret));
        return ret;
    }

    adc_initialized = true;
    ESP_LOGI(TAG, "ADC hardware sampling initialized successfully");
    return ESP_OK;
}

void setup()
{
    printf("Starting ADC Hardware Sampling Demo\n");

    // 初始化ADC硬件
    esp_err_t ret = adc_hardware_init();
    if (ret != ESP_OK) {
        printf("ADC hardware init failed: %s\n", esp_err_to_name(ret));
        return;
    }

    // 获取当前任务句柄用于通知
    adc_task_handle = xTaskGetCurrentTaskHandle();
}

void loop()
{
    float adjusted_voltage = ReadVoltageHardware();
    printf("Hardware sampled voltage = %.3f V\n", adjusted_voltage);
    printf("\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
}

// 硬件定时器采样函数
float ReadVoltageHardware(void)
{
    if (!adc_initialized) {
        ESP_LOGE(TAG, "ADC not initialized");
        return 0.0;
    }

    // 重置采样状态
    sample_count = 0;
    sampling_complete = false;
    memset(adc_samples, 0, sizeof(adc_samples));

    ESP_LOGI(TAG, "Starting hardware sampling...");

    // 启动定时器开始采样
    timer_start(TIMER_GROUP, TIMER_IDX);

    // 等待采样完成（最多等待5秒）
    uint32_t notification_value = ulTaskNotifyTake(pdTRUE, pdMS_TO_TICKS(5000));

    // 停止定时器
    timer_pause(TIMER_GROUP, TIMER_IDX);

    if (notification_value == 0) {
        ESP_LOGE(TAG, "Sampling timeout");
        return 0.0;
    }

    if (!sampling_complete || sample_count != NO_OF_SAMPLES) {
        ESP_LOGE(TAG, "Sampling incomplete: %d/%d samples", sample_count, NO_OF_SAMPLES);
        return 0.0;
    }

    // 计算平均值
    uint64_t sum = 0;
    for (int i = 0; i < NO_OF_SAMPLES; i++) {
        sum += adc_samples[i];
    }
    uint32_t adc_reading = sum / NO_OF_SAMPLES;

    ESP_LOGI(TAG, "Hardware sampling completed: %d samples, average = %d",
             NO_OF_SAMPLES, adc_reading);

    // 转换为电压
    uint32_t voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_chars);

    return voltage / 1000.0; // 转换为伏特
}

// 保留原始软件采样函数作为对比
float ReadVoltageSoftware(void)
{
    uint32_t adc_reading = 0;

    // 软件循环采样
    for (int i = 0; i < NO_OF_SAMPLES; i++) {
        adc_reading += adc1_get_raw(adc1_channel);
    }
    adc_reading /= NO_OF_SAMPLES;

    uint32_t voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_chars);
    return voltage / 1000.0;
}

// ADC反初始化函数
void adc_hardware_deinit(void)
{
    if (adc_initialized) {
        timer_pause(TIMER_GROUP, TIMER_IDX);
        timer_disable_intr(TIMER_GROUP, TIMER_IDX);
        timer_deinit(TIMER_GROUP, TIMER_IDX);
        adc_initialized = false;
        ESP_LOGI(TAG, "ADC hardware deinitialized");
    }
}
