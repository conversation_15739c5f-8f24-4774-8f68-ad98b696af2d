##
# @file CMakeLists.txt
# @brief 
#/

# Include ESP-IDF build system
cmake_minimum_required(VERSION 3.16)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
set(APP_PATH ${CMAKE_CURRENT_LIST_DIR})

# APP_NAME
get_filename_component(APP_NAME ${APP_PATH} NAME)

# APP_SRCS
aux_source_directory(${APP_PATH}/src APP_SRCS)

list(APPEND APP_SRCS 
    ${APP_PATH}/libqrencode/qrencode.c
    ${APP_PATH}/libqrencode/qrinput.c
    ${APP_PATH}/libqrencode/bitstream.c
    ${APP_PATH}/libqrencode/qrspec.c
    ${APP_PATH}/libqrencode/rsecc.c
    ${APP_PATH}/libqrencode/split.c
    ${APP_PATH}/libqrencode/mask.c
    ${APP_PATH}/libqrencode/mqrspec.c
    ${APP_PATH}/libqrencode/mmask.c
)

add_definitions(-DSTATIC_IN_RELEASE=static)
add_definitions(-DMAJOR_VERSION=4 -DMINOR_VERSION=1 -DMICRO_VERSION=1 -DVERSION=\"4.1.1\")

# APP_INC
set(APP_INC 
    ${APP_PATH}/libqrencode
    ${APP_PATH}/include
    ${APP_PATH}/lib/common/include
    ${APP_PATH}/lib/tal_bluetooth/include
    ${APP_PATH}/lib/tal_network/include
    ${APP_PATH}/lib/tuya_cloud_service/include
    ${APP_PATH}/lib/tuya_cloud_service/netmgr
    ${APP_PATH}/lib/tuya_cloud_service/cloud
    ${APP_PATH}/lib/tuya_cloud_service/ble
    ${APP_PATH}/lib/tuya_cloud_service/lan
)

########################################
# Target Configure
########################################
idf_component_register(
    SRCS ${APP_SRCS}
    INCLUDE_DIRS ${APP_INC}
    REQUIRES 
        freertos
        esp_netif
        esp_event
        nvs_flash
        esp_wifi
        lwip
)

add_executable(${APP_NAME} ${APP_SRCS})
target_link_libraries(${APP_NAME} PRIVATE 
    ${IDF_COMPONENTS}
    ${APP_LIBS}
)

include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(Switch)