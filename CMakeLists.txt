##
# @file CMakeLists.txt
# @brief ESP32 Tuya IoT Project CMakeLists
#/

# ESP-IDF CMake minimum version
cmake_minimum_required(VERSION 3.16)

# Set project path
set(PROJECT_PATH ${CMAKE_CURRENT_LIST_DIR})

# Project name
set(PROJECT_NAME "esp32-tuyaopen")

# Add compile definitions
add_definitions(-DSTATIC_IN_RELEASE=static)
add_definitions(-DMAJOR_VERSION=4 -DMINOR_VERSION=1 -DMICRO_VERSION=1 -DVERSION=\"4.1.1\")
add_definitions(-DTUYA_DEBUG_LOGS=1)

# Set component directories
set(EXTRA_COMPONENT_DIRS
    ${PROJECT_PATH}/lib/common
    ${PROJECT_PATH}/lib/tal_system
    ${PROJECT_PATH}/lib/tal_driver
    ${PROJECT_PATH}/lib/tal_kv
    ${PROJECT_PATH}/lib/tal_network
    ${PROJECT_PATH}/lib/tal_wifi
    ${PROJECT_PATH}/lib/tal_wired
    ${PROJECT_PATH}/lib/tal_bluetooth
    ${PROJECT_PATH}/lib/tal_cli
    ${PROJECT_PATH}/lib/tal_security
    ${PROJECT_PATH}/lib/tuya_cloud_service
    ${PROJECT_PATH}/lib/tuyaos_adapter
    ${PROJECT_PATH}/lib/tuya_ai_basic
    ${PROJECT_PATH}/lib/peripherals
    ${PROJECT_PATH}/lib/coreHTTP
    ${PROJECT_PATH}/lib/coreMQTT
    ${PROJECT_PATH}/lib/libhttp
    ${PROJECT_PATH}/lib/libmqtt
    ${PROJECT_PATH}/lib/liblwip
    ${PROJECT_PATH}/lib/liblvgl
    ${PROJECT_PATH}/lib/libqrencode
    ${PROJECT_PATH}/lib/utils
)

# Include ESP-IDF build system
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# Define the project
project(${PROJECT_NAME})