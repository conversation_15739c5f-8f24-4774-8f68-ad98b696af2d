##
# @file CMakeLists.txt
# @brief ESP32 Tuya IoT Project CMakeLists
#/

# ESP-IDF CMake minimum version
cmake_minimum_required(VERSION 3.16)

# Add compile definitions
add_definitions(-DSTATIC_IN_RELEASE=static)
add_definitions(-DMAJOR_VERSION=4 -DMINOR_VERSION=1 -DMICRO_VERSION=1 -DVERSION=\"4.1.1\")
add_definitions(-DTUYA_DEBUG_LOGS=1)

# Set component directories - only include components that have proper CMakeLists.txt
set(EXTRA_COMPONENT_DIRS
    ${CMAKE_CURRENT_LIST_DIR}/lib/common
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_system
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_driver
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_kv
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_network
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_wifi
    ${CMAKE_CURRENT_LIST_DIR}/lib/tal_bluetooth
    ${CMAKE_CURRENT_LIST_DIR}/lib/tuya_cloud_service
    ${CMAKE_CURRENT_LIST_DIR}/lib/coreHTTP
    ${CMAKE_CURRENT_LIST_DIR}/lib/coreMQTT
    ${CMAKE_CURRENT_LIST_DIR}/lib/utils
)

# Include ESP-IDF build system
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# Define the project
project(esp32-tuyaopen)