##
# @file CMakeLists.txt
# @brief TAL KV component for ESP-IDF
#/

# Get source files
set(component_srcs
    "${CMAKE_CURRENT_SOURCE_DIR}/src/tal_kv.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/kv_serialize.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/littlefs/lfs_util.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/littlefs/lfs.c"
)

# Set include directories
set(component_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
    "${CMAKE_CURRENT_SOURCE_DIR}/littlefs"
)

set(component_priv_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/port"
)

# Register ESP-IDF component
idf_component_register(
    SRCS ${component_srcs}
    INCLUDE_DIRS ${component_includes}
    PRIV_INCLUDE_DIRS ${component_priv_includes}
    REQUIRES
        freertos
        spi_flash
        tal_system
)

# Add compile definitions
target_compile_definitions(${COMPONENT_LIB} PRIVATE -DLFS_CONFIG=lfs_config.h)