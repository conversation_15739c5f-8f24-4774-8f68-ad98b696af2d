/**
 * @file tuya_config_cli.c
 * @brief Tuya configuration CLI commands
 */

#include "tuya_config_cli.h"
#include "tuya_config_manager.h"
#include "tuya_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "esp_system.h"
#include <string.h>
#include <stdio.h>
#include "shipping.h"

static const char *TAG = "TUYA_CONFIG_CLI";

// CLI配置
#define CLI_UART_NUM        UART_NUM_0
#define CLI_UART_BAUD_RATE  115200
#define CLI_BUF_SIZE        1024
#define CLI_QUEUE_SIZE      20
#define CLI_MAX_CMD_LEN     256

// CLI状态
static QueueHandle_t cli_uart_queue;
static TaskHandle_t cli_task_handle = NULL;
static bool cli_initialized = false;
static char cli_command_buffer[CLI_MAX_CMD_LEN];
static int cli_buffer_pos = 0;

// CLI命令处理函数
static void cmd_tuya_config_show(void)
{
    tuya_config_print_current();
}

static void cmd_tuya_config_set_prodkey(const char *prodkey)
{
    if (!prodkey || strlen(prodkey) == 0) {
        TY_LOGE("Invalid PRODKEY parameter");
        return;
    }
    
    int ret = tuya_config_update_prodkey(prodkey);
    if (ret == OPRT_OK) {
        TY_LOGI("PRODKEY updated successfully: %s", prodkey);
    } else {
        TY_LOGE("Failed to update PRODKEY: %d", ret);
    }
}

static void cmd_tuya_config_set_uuid(const char *uuid)
{
    if (!uuid || strlen(uuid) == 0) {
        TY_LOGE("Invalid UUID parameter");
        return;
    }
    
    int ret = tuya_config_update_uuid(uuid);
    if (ret == OPRT_OK) {
        TY_LOGI("UUID updated successfully: %s", uuid);
    } else {
        TY_LOGE("Failed to update UUID: %d", ret);
    }
}

static void cmd_tuya_config_set_authkey(const char *authkey)
{
    if (!authkey || strlen(authkey) == 0) {
        TY_LOGE("Invalid AuthKey parameter");
        return;
    }
    
    int ret = tuya_config_update_authkey(authkey);
    if (ret == OPRT_OK) {
        TY_LOGI("AuthKey updated successfully: %s", authkey);
    } else {
        TY_LOGE("Failed to update AuthKey: %d", ret);
    }
}

static void cmd_tuya_config_reset(void)
{
    TY_LOGI("Resetting Tuya configuration to defaults...");
    int ret = tuya_config_reset_to_defaults();
    if (ret == OPRT_OK) {
        TY_LOGI("Configuration reset successfully");
        tuya_config_print_current();
    } else {
        TY_LOGE("Failed to reset configuration: %d", ret);
    }
}

static void cmd_tuya_config_save_current(void)
{
    const tuya_config_data_t *config = tuya_config_get();
    int ret = tuya_config_save(config);
    if (ret == OPRT_OK) {
        TY_LOGI("Configuration saved successfully");
    } else {
        TY_LOGE("Failed to save configuration: %d", ret);
    }
}

static void cmd_tuya_config_restart(void)
{
    TY_LOGI("Device restart requested");
    printf("\n=== Device Restart ===\n");
    printf("Restarting device in 3 seconds...\n");
    printf("3...\n");
    fflush(stdout);
    vTaskDelay(pdMS_TO_TICKS(1000));

    printf("2...\n");
    fflush(stdout);
    vTaskDelay(pdMS_TO_TICKS(1000));

    printf("1...\n");
    fflush(stdout);
    vTaskDelay(pdMS_TO_TICKS(1000));

    printf("Restarting now!\n");
    fflush(stdout);

    // 使用ESP-IDF的重启函数
    esp_restart();
}

// 简单的命令解析器
void tuya_config_cli_process_command(const char *command)
{
    if (!command || strlen(command) == 0) {
        return;
    }
    
    TY_LOGI("Processing CLI command: %s", command);
    
    // 显示配置
    if (strcmp(command, "config show") == 0) {
        cmd_tuya_config_show();
    }
    // 重置配置
    else if (strcmp(command, "config reset") == 0) {
        cmd_tuya_config_reset();
    }
    // 保存配置
    else if (strcmp(command, "config save") == 0) {
        cmd_tuya_config_save_current();
    }
    else if (strncmp(command, "config set prodkey ", 19) == 0) {
        const char *prodkey = command + 19;
        cmd_tuya_config_set_prodkey(prodkey);
    }
    // 设置UUID
    else if (strncmp(command, "config set uuid ", 16) == 0) {
        const char *uuid = command + 16;
        cmd_tuya_config_set_uuid(uuid);
    }
    // 设置AuthKey
    else if (strncmp(command, "config set authkey ", 19) == 0) {
        const char *authkey = command + 19;
        cmd_tuya_config_set_authkey(authkey);
    }
    // 清除WiFi信息
    else if (strcmp(command, "config clear wifi") == 0) {
        factory_reset();
    }
    // 清除涂鸦激活信息
    else if (strcmp(command, "config clear tuya") == 0) {
        clear_tuya_activated_info();
    }
    // 重置到出厂模式
    else if (strcmp(command, "config reset factory") == 0) {
        factory_reset();
    }
    // 重启设备
    else if (strcmp(command, "config restart") == 0 || strcmp(command, "restart") == 0) {
        cmd_tuya_config_restart();
    }
    // 帮助信息
    else if (strcmp(command, "config help") == 0 || strcmp(command, "help") == 0) {
        tuya_config_cli_print_help();
    }
    else {
        TY_LOGW("Unknown command: %s", command);
        TY_LOGI("Type 'help' for available commands");
    }
}

void tuya_config_cli_print_help(void)
{
    TY_LOGI("=== Tuya Configuration CLI Commands ===");
    TY_LOGI("config show                    - Show current configuration");
    TY_LOGI("config set prodkey <prodkey>   - Set device product key");
    TY_LOGI("config set uuid <uuid>         - Set device UUID");
    TY_LOGI("config set authkey <authkey>   - Set device AuthKey");
    TY_LOGI("config clear wifi              - Clear WiFi credentials");
    TY_LOGI("config clear tuya              - Clear Tuya activated info");
    TY_LOGI("config reset factory           - Reset to factory defaults");
    TY_LOGI("config save                    - Save current config to NVS");
    TY_LOGI("config reset                   - Reset to default values");
    TY_LOGI("config restart                 - Restart the device");
    TY_LOGI("restart                        - Restart the device (short)");
    TY_LOGI("help                           - Show this help");
    TY_LOGI("=======================================");
    TY_LOGI("");
    TY_LOGI("Examples:");
    TY_LOGI("  config set uuid tuyac0828c3dfbc6a170");
    TY_LOGI("  config set authkey gxQPs3qtR0pNSx12hh0floVG117uUnJL");
    TY_LOGI("  config show");
    TY_LOGI("  config save");
    TY_LOGI("  restart");
}

// 预设配置函数
void tuya_config_cli_set_test_config(void)
{
    TY_LOGI("Setting test configuration...");
    
    tuya_config_data_t test_config = {
        .product_key = "ff1lwoe4t5rkeg5m",
        .device_uuid = "tuyac0828c3dfbc6a170",
        .device_authkey = "gxQPs3qtR0pNSx12hh0floVG117uUnJL",
        .software_ver = "1.0.0"
    };
    
    int ret = tuya_config_save(&test_config);
    if (ret == OPRT_OK) {
        TY_LOGI("Test configuration saved successfully");
        tuya_config_print_current();
    } else {
        TY_LOGE("Failed to save test configuration: %d", ret);
    }
}

// CLI任务函数
static void cli_task(void *pvParameters)
{
    uart_event_t event;
    uint8_t *dtmp = (uint8_t*) malloc(CLI_BUF_SIZE);
    int len;

    TY_LOGI("CLI task started, listening for commands...");
    printf("\n=== Tuya Configuration CLI ===\n");
    printf("Type 'help' for available commands\n");
    printf("\ntuya> ");
    fflush(stdout);

    while (1) {
        // 等待UART事件
        if (xQueueReceive(cli_uart_queue, (void*)&event, portMAX_DELAY)) {
            bzero(dtmp, CLI_BUF_SIZE);

            switch (event.type) {
                case UART_DATA:
                    // 读取UART数据
                    len = uart_read_bytes(CLI_UART_NUM, dtmp, event.size, portMAX_DELAY);
                    if (len > 0) {
                        // 处理接收到的字符
                        for (int i = 0; i < len; i++) {
                            char ch = dtmp[i];

                            if (ch == '\r' || ch == '\n') {
                                // 回车键 - 处理命令
                                if (cli_buffer_pos > 0) {
                                    cli_command_buffer[cli_buffer_pos] = '\0';
                                    printf("\n");

                                    // 处理命令
                                    tuya_config_cli_process_command(cli_command_buffer);

                                    // 重置缓冲区
                                    cli_buffer_pos = 0;
                                    memset(cli_command_buffer, 0, CLI_MAX_CMD_LEN);
                                }
                                printf("\ntuya> ");

                                fflush(stdout);
                            }
                            else if (ch == '\b' || ch == 127) {
                                // 退格键
                                if (cli_buffer_pos > 0) {
                                    cli_buffer_pos--;
                                    cli_command_buffer[cli_buffer_pos] = '\0';
                                    printf("\b \b");
                                    fflush(stdout);
                                }
                            }
                            else if (ch >= 32 && ch <= 126) {
                                // 可打印字符
                                if (cli_buffer_pos < CLI_MAX_CMD_LEN - 1) {
                                    cli_command_buffer[cli_buffer_pos] = ch;
                                    cli_buffer_pos++;
                                    printf("%c", ch);
                                    fflush(stdout);
                                }
                            }
                        }
                    }
                    break;

                case UART_FIFO_OVF:
                    TY_LOGW("UART FIFO overflow");
                    uart_flush_input(CLI_UART_NUM);
                    xQueueReset(cli_uart_queue);
                    break;

                case UART_BUFFER_FULL:
                    TY_LOGW("UART ring buffer full");
                    uart_flush_input(CLI_UART_NUM);
                    xQueueReset(cli_uart_queue);
                    break;

                default:
                    break;
            }
        }
    }

    free(dtmp);
    vTaskDelete(NULL);
}

void tuya_config_cli_init(void)
{
    if (cli_initialized) {
        TY_LOGW("CLI already initialized");
        return;
    }

    TY_LOGI("Initializing Tuya Configuration CLI...");

    // 配置UART参数
    uart_config_t uart_config = {
        .baud_rate = CLI_UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
    };

    // 安装UART驱动
    esp_err_t ret = uart_driver_install(CLI_UART_NUM, CLI_BUF_SIZE * 2, CLI_BUF_SIZE * 2, CLI_QUEUE_SIZE, &cli_uart_queue, 0);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to install UART driver: %s", esp_err_to_name(ret));
        return;
    }

    // 配置UART参数
    ret = uart_param_config(CLI_UART_NUM, &uart_config);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to configure UART: %s", esp_err_to_name(ret));
        return;
    }

    // 创建CLI任务
    BaseType_t task_ret = xTaskCreate(cli_task, "cli_task", 4096, NULL, 10, &cli_task_handle);
    if (task_ret != pdPASS) {
        TY_LOGE("Failed to create CLI task");
        return;
    }

    cli_initialized = true;
    TY_LOGI("Tuya Configuration CLI initialized successfully");
}

void tuya_config_cli_deinit(void)
{
    if (!cli_initialized) {
        return;
    }

    TY_LOGI("Deinitializing CLI...");

    // 删除任务
    if (cli_task_handle) {
        vTaskDelete(cli_task_handle);
        cli_task_handle = NULL;
    }

    // 卸载UART驱动
    uart_driver_delete(CLI_UART_NUM);

    cli_initialized = false;
    TY_LOGI("CLI deinitialized");
}

bool tuya_config_cli_is_initialized(void)
{
    return cli_initialized;
}

void tuya_config_cli_send_response(const char *response)
{
    if (response && cli_initialized) {
        printf("%s\n", response);
        fflush(stdout);
    }
}

void tuya_config_cli_restart_device(void)
{
    cmd_tuya_config_restart();
}
