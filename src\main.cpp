#include <Arduino.h>
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <WiFi.h>

#include "tuya_log.h"
#include "tuya_config.h"
#include "tuya_iot.h"
#include "cJSON.h"
#include "tuya_wifi_provisioning.h"
#include "improv.h"
#include "storage_interface.h"

#define SOFTWARE_VER     "1.0.0"
 
/* Tuya device handle */
tuya_iot_client_t client;
 
#define SWITCH_DP_ID_KEY "1"
improv::ImprovComponent improvComponent;

void example_qrcode_print(const char* productkey, const char* uuid)
{
	TY_LOGI("https://smartapp.tuya.com/s/p?p=%s&uuid=%s&v=2.0", productkey, uuid);
	TY_LOGI("(Use this URL to generate a static QR code for the Tuya APP scan code binding)");
}
 
/* Hardware switch control function */
void hardware_switch_set(bool value)
{
    if (value == true) {
        TY_LOGI("Switch ON");
    } else {
        TY_LOGI("Switch OFF");
    }
}
 
/* DP data reception processing function */
void tuya_iot_dp_download(tuya_iot_client_t* client, const char* json_dps)
{
    TY_LOGD("Data point download value:%s", json_dps);
 
    /* Parsing json string to cJSON object */
    cJSON* dps = cJSON_Parse(json_dps);
    if (dps == NULL) {
        TY_LOGE("JSON parsing error, exit!");
        return;
    }
 
    /* Process dp data */
    cJSON* switch_obj = cJSON_GetObjectItem(dps, SWITCH_DP_ID_KEY);
    if (cJSON_IsTrue(switch_obj)) {
        hardware_switch_set(true);
 
    } else if (cJSON_IsFalse(switch_obj)) {
        hardware_switch_set(false);
    }
 
    /* relese cJSON DPS object */
    cJSON_Delete(dps);
 
    /* Report the received data to synchronize the switch status. */
    tuya_iot_dp_report_json(client, json_dps);
}

/* Tuya OTA event callback */
void user_upgrade_notify_on(tuya_iot_client_t* client, cJSON* upgrade)
{
    TY_LOGI("----- Upgrade information -----");
    TY_LOGI("OTA Channel: %d", cJSON_GetObjectItem(upgrade, "type")->valueint);
    TY_LOGI("Version: %s", cJSON_GetObjectItem(upgrade, "version")->valuestring);
    TY_LOGI("Size: %s", cJSON_GetObjectItem(upgrade, "size")->valuestring);
    TY_LOGI("MD5: %s", cJSON_GetObjectItem(upgrade, "md5")->valuestring);
    TY_LOGI("HMAC: %s", cJSON_GetObjectItem(upgrade, "hmac")->valuestring);
    TY_LOGI("URL: %s", cJSON_GetObjectItem(upgrade, "url")->valuestring);
    TY_LOGI("HTTPS URL: %s", cJSON_GetObjectItem(upgrade, "httpsUrl")->valuestring);
}

/* Tuya SDK event callback */
static void user_event_handler_on(tuya_iot_client_t* client, tuya_event_msg_t* event)
{
    TY_LOGD("Tuya Event ID:%d(%s)", event->id, EVENT_ID2STR(event->id));
    switch (event->id) {
    case TUYA_EVENT_BIND_START:
        TY_LOGI("Device START BIND!");
        /* Print the QRCode for Tuya APP bind */
        break;

    case TUYA_EVENT_MQTT_CONNECTED:
        TY_LOGI("Device MQTT Connected!");
        break;

    case TUYA_EVENT_DP_RECEIVE:
        tuya_iot_dp_download(client, (const char*)event->value.asString);
        break;

    case TUYA_EVENT_UPGRADE_NOTIFY:
        user_upgrade_notify_on(client, event->value.asJSON);
        break;

    case TUYA_EVENT_TIMESTAMP_SYNC:
        TY_LOGI("Sync timestamp:%d", event->value.asInteger);
        break;

    default:
        break;
    }
}

extern "C" {
    bool isWifiConnected() {
        return (WiFi.status() == WL_CONNECTED);
    }
}

void wifi_info_cb(wifi_info_t wifi_info)
{
    TY_LOGI("wifi ssid: %s", wifi_info.ssid);
    TY_LOGI("wifi pwd: %s", wifi_info.pwd);
    if (!improvComponent.isWifiConnected()) {
        improvComponent.wifi_connect(
            std::string(reinterpret_cast<const char*>(wifi_info.ssid)),
            std::string(reinterpret_cast<const char*>(wifi_info.pwd))
        );
    }
}

void setup()
{
    int ret = OPRT_OK;

    Serial.begin(115200);
    TY_LOGI("Starting ESP32 Tuya IoT Device...");
    // 初始化存储系统
    TY_LOGI("Initializing storage...");
    ret = local_storage_init();
    if (ret != OPRT_OK) {
        Serial.printf("Storage init failed: %d\n", ret);
        ESP.restart();
        return;
    }

    // 初始化WiFi (但不连接)
    WiFi.mode(WIFI_STA);
    vTaskDelay(pdMS_TO_TICKS(100));

    // 初始化 Improv 组件
    improvComponent.setup();
    vTaskDelay(pdMS_TO_TICKS(100));

    /* 定义 Tuya 设备配置 */
    const tuya_iot_config_t config = {
        .productkey = TUYA_PRODUCT_KEY,
        .uuid = TUYA_DEVICE_UUID,
        .authkey = TUYA_DEVICE_AUTHKEY,
        .software_ver = SOFTWARE_VER,
        .event_handler = user_event_handler_on
    };

    /* 初始化 Tuya 设备及调试输出等级 */
    log_set_level(LOG_INFO);
    TY_LOGI("Initializing Tuya IoT...");
    ret = tuya_iot_init(&client, &config);
    if (ret != OPRT_OK) {
        Serial.printf("Tuya IoT init failed: %d\n", ret);
        ESP.restart();
        return;
    }

    TY_LOGI("Starting Tuya IoT...");
    /* 启动 Tuya IoT 任务 */
    tuya_iot_start(&client);

    TY_LOGI("Starting WiFi provisioning...");
    /* Start wifi provisioning, ble get wifi provisioning params */
    tuya_wifi_provisioning(&client, WIFI_PROVISIONING_MODE_BLE, &wifi_info_cb);
}

void loop()
{
    /* Loop to receive packets, and handles client keepalive */
    tuya_iot_yield(&client);
}
