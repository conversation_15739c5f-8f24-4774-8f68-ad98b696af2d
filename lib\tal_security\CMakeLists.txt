##
# @file CMakeLists.txt
# @brief TAL Security component for ESP-IDF
#/

# Get all source files
file(GLOB_RECURSE component_srcs
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.c"
)

# Set include directories
set(component_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

# Register ESP-IDF component
idf_component_register(
    SRCS ${component_srcs}
    INCLUDE_DIRS ${component_includes}
    REQUIRES
        freertos
        mbedtls
        tal_system
)

