; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32@6.11.0
board = esp32-c3-devkitm-1
framework = espidf
board_build.mcu = esp32c3
board_build.flash_mode = qio
board_build.flash_size = 4MB
board_build.partitions = partitions_4M.csv
build_flags = 
    -Isrc
    -Iinclude
    -Ilib/common/include
    -Ilib/tal_bluetooth/include
    -Ilib/tal_network/include
    -Ilib/tuya_cloud_service/include
    -Ilib/tuya_cloud_service/netmgr
    -Ilib/tuya_cloud_service/cloud
    -Ilib/tuya_cloud_service/ble
    -Ilib/tuya_cloud_service/lan
    -Ilib/coreHTTP/include
    -Ilib/coreJSON/include
    -Ilib/coreMQTT/include
    -Ilib/mbedtls/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/porting/npl/freertos/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/gap/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/gatt/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/ans/include
    -I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/esp_port/esp-hci/include
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DTUYA_DEBUG_LOGS=1

lib_deps = 
    utils
	h2zero/NimBLE-Arduino@1.4.3
