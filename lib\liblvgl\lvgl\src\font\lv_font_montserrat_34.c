/*******************************************************************************
 * Size: 34 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 34 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_34.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_34
    #define LV_FONT_MONTSERRAT_34 1
#endif

#if LV_FONT_MONTSERRAT_34

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x5f, 0xff, 0x74, 0xff, 0xf7, 0x4f, 0xff, 0x63,
    0xff, 0xf5, 0x2f, 0xff, 0x52, 0xff, 0xf4, 0x1f,
    0xff, 0x31, 0xff, 0xf3, 0xf, 0xff, 0x20, 0xff,
    0xf1, 0xf, 0xff, 0x10, 0xef, 0xf0, 0xd, 0xff,
    0x0, 0xdf, 0xf0, 0xc, 0xfe, 0x0, 0xcf, 0xd0,
    0x1, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x10, 0x2e, 0xff, 0x49, 0xff, 0xfb, 0x8f,
    0xff, 0x90, 0xbf, 0xc1,

    /* U+0022 "\"" */
    0xdf, 0xf0, 0x0, 0xbf, 0xf2, 0xdf, 0xf0, 0x0,
    0xaf, 0xf1, 0xcf, 0xe0, 0x0, 0xaf, 0xf1, 0xcf,
    0xe0, 0x0, 0xaf, 0xf0, 0xcf, 0xe0, 0x0, 0x9f,
    0xf0, 0xbf, 0xd0, 0x0, 0x9f, 0xf0, 0xbf, 0xd0,
    0x0, 0x8f, 0xf0, 0xbf, 0xc0, 0x0, 0x8f, 0xf0,
    0xaf, 0xc0, 0x0, 0x8f, 0xe0, 0x1, 0x0, 0x0,
    0x1, 0x10,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0x0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0xbf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0xd, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfa, 0x0, 0x0, 0x0, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x77, 0x77, 0x9f,
    0xf8, 0x77, 0x77, 0x7b, 0xff, 0x77, 0x77, 0x70,
    0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x8f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfd, 0x0, 0x0, 0x0, 0xcf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf9, 0x0, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0x77, 0x77, 0x7f,
    0xfa, 0x77, 0x77, 0x79, 0xff, 0x97, 0x77, 0x60,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0xb, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf7, 0x0, 0x0, 0x1, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xad, 0xff, 0xff, 0xdb, 0x71, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x20, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x2f, 0xff, 0xe6, 0x15, 0xfd,
    0x1, 0x4a, 0xff, 0x20, 0xa, 0xff, 0xe2, 0x0,
    0x5f, 0xd0, 0x0, 0x2, 0x70, 0x0, 0xef, 0xf7,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x50, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe3, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf9, 0x35,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xcf, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd1, 0x7e, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x9, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0xd, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0xaf,
    0xfb, 0x2, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0xc, 0xff, 0x90, 0xdd, 0x30, 0x0, 0x5, 0xfd,
    0x0, 0x4, 0xff, 0xf5, 0x4f, 0xff, 0xb5, 0x10,
    0x5f, 0xd0, 0x28, 0xff, 0xfd, 0x3, 0xef, 0xff,
    0xff, 0xfe, 0xff, 0xef, 0xff, 0xfe, 0x20, 0x1,
    0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x5, 0x9d, 0xef, 0xff, 0xfd, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x7, 0xdf, 0xeb, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xb0, 0x0, 0x0, 0xa, 0xfd, 0x30, 0x7, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x8f, 0xf1, 0x0, 0x0,
    0x3, 0xff, 0x30, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0xd, 0xfb,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x9, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0xef, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x9f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x30, 0x0, 0x9, 0xfc, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfd, 0x30, 0x7, 0xff, 0x40, 0x1e, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0x70, 0xa, 0xfe, 0x10, 0x6, 0xbe, 0xda,
    0x30, 0x0, 0x0, 0x7, 0xdf, 0xeb, 0x40, 0x5,
    0xff, 0x40, 0xb, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x90, 0x9,
    0xfe, 0x50, 0x18, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xe0, 0x1, 0xff, 0x40, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf4, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xf9, 0x0, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x30, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0x50, 0x0, 0x0, 0x1, 0xff, 0x80, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x2, 0xff, 0x20,
    0x0, 0x7, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x10, 0x3, 0xff,
    0x50, 0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xcc, 0xff, 0x90, 0x0, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xdf, 0xfb, 0x50, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x5a, 0xef, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x96, 0x6b, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0x0, 0x7f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf5, 0x0, 0x7, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x32, 0xbf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0xcf, 0xff, 0x30, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0xc, 0xff, 0xf4, 0x0, 0x2,
    0xff, 0x60, 0x6, 0xff, 0xe3, 0x0, 0x0, 0xcf,
    0xff, 0x40, 0x7, 0xff, 0x60, 0xe, 0xff, 0x50,
    0x0, 0x0, 0xb, 0xff, 0xf4, 0xc, 0xff, 0x10,
    0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x8f, 0xfc, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf4, 0x0, 0x6f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf5, 0x0, 0xc, 0xff, 0xf9, 0x10,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0x50, 0x1,
    0xdf, 0xff, 0xfd, 0xbb, 0xcf, 0xff, 0xfe, 0x3a,
    0xff, 0xf5, 0x0, 0x1a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x27,
    0xce, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x9, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xdf, 0xfd, 0xff, 0xcf, 0xec, 0xfe, 0xcf, 0xeb,
    0xfd, 0xbf, 0xdb, 0xfc, 0xaf, 0xc0, 0x10,

    /* U+0028 "(" */
    0x0, 0x1, 0xef, 0xf2, 0x0, 0x9, 0xff, 0x90,
    0x0, 0x1f, 0xff, 0x20, 0x0, 0x7f, 0xfa, 0x0,
    0x0, 0xdf, 0xf4, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x8, 0xff, 0xa0, 0x0, 0xc, 0xff, 0x60, 0x0,
    0xf, 0xff, 0x30, 0x0, 0x3f, 0xff, 0x0, 0x0,
    0x5f, 0xfd, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x0,
    0x9f, 0xf9, 0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0,
    0xbf, 0xf8, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0,
    0xaf, 0xf8, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x8f, 0xfa, 0x0, 0x0, 0x5f, 0xfd, 0x0, 0x0,
    0x3f, 0xff, 0x0, 0x0, 0xf, 0xff, 0x30, 0x0,
    0xc, 0xff, 0x60, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x2, 0xff, 0xe0, 0x0, 0x0, 0xdf, 0xf4, 0x0,
    0x0, 0x7f, 0xfa, 0x0, 0x0, 0xe, 0xff, 0x20,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0xef, 0xf2,

    /* U+0029 ")" */
    0xaf, 0xf7, 0x0, 0x0, 0x2f, 0xff, 0x10, 0x0,
    0xa, 0xff, 0x80, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x0, 0xdf, 0xf5, 0x0, 0x0, 0x7f, 0xfb, 0x0,
    0x0, 0x3f, 0xff, 0x0, 0x0, 0xe, 0xff, 0x40,
    0x0, 0xb, 0xff, 0x70, 0x0, 0x8, 0xff, 0xb0,
    0x0, 0x6, 0xff, 0xd0, 0x0, 0x3, 0xff, 0xf0,
    0x0, 0x2, 0xff, 0xf1, 0x0, 0x1, 0xff, 0xf2,
    0x0, 0x0, 0xff, 0xf3, 0x0, 0x0, 0xff, 0xf4,
    0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0xff, 0xf3,
    0x0, 0x1, 0xff, 0xf2, 0x0, 0x2, 0xff, 0xf1,
    0x0, 0x3, 0xff, 0xf0, 0x0, 0x6, 0xff, 0xd0,
    0x0, 0x8, 0xff, 0xb0, 0x0, 0xb, 0xff, 0x70,
    0x0, 0xe, 0xff, 0x40, 0x0, 0x3f, 0xff, 0x0,
    0x0, 0x7f, 0xfa, 0x0, 0x0, 0xdf, 0xf4, 0x0,
    0x3, 0xff, 0xe0, 0x0, 0xa, 0xff, 0x70, 0x0,
    0x2f, 0xfe, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x0, 0x0, 0x6, 0x60, 0x3,
    0xfc, 0x0, 0x19, 0x21, 0xff, 0xc3, 0x3f, 0xc0,
    0x7f, 0xfa, 0x8, 0xff, 0xfb, 0xfe, 0xdf, 0xfd,
    0x40, 0x1, 0x9f, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xf7, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xfd, 0x40, 0x2e, 0xff, 0xb5, 0xfc,
    0x6e, 0xff, 0xa0, 0xcd, 0x50, 0x3f, 0xc0, 0x8,
    0xf6, 0x1, 0x0, 0x3, 0xfc, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x87, 0x0, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x6b, 0xb3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x7a, 0xaa, 0xaa, 0xdf, 0xfc, 0xaa, 0xaa, 0xa5,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0x54, 0x0, 0xd, 0xff, 0x90, 0x4f, 0xff,
    0xf0, 0x4f, 0xff, 0xf0, 0xa, 0xff, 0xc0, 0x1,
    0xff, 0x70, 0x5, 0xff, 0x20, 0x9, 0xfc, 0x0,
    0xd, 0xf7, 0x0, 0x1f, 0xf1, 0x0,

    /* U+002D "-" */
    0xc, 0xcc, 0xcc, 0xcc, 0xcc, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x10,

    /* U+002E "." */
    0x2, 0x87, 0x0, 0x1f, 0xff, 0xc0, 0x6f, 0xff,
    0xf1, 0x3f, 0xff, 0xe0, 0x7, 0xed, 0x40,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x88, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x0, 0x39, 0xcf, 0xfe, 0xb7, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xc5, 0x10, 0x26, 0xef, 0xff,
    0x80, 0x0, 0x0, 0x8f, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0x30, 0x0, 0x1f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xfb, 0x0, 0x7,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf2, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x70, 0xf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xd0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x5, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x4, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf0, 0x3f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x7f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xb0, 0x0,
    0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf3, 0x0, 0x0, 0xd, 0xff, 0xfb, 0x41, 0x1,
    0x6e, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xff, 0xeb,
    0x71, 0x0, 0x0, 0x0,

    /* U+0031 "1" */
    0xbf, 0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff,
    0xff, 0xf0, 0xae, 0xee, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x5, 0xff, 0xf0,

    /* U+0032 "2" */
    0x0, 0x0, 0x27, 0xbd, 0xef, 0xec, 0x82, 0x0,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x4, 0xff, 0xff, 0xa5,
    0x10, 0x2, 0x6e, 0xff, 0xf8, 0x0, 0x7, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xf0, 0x0,
    0x4, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xe5, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5,

    /* U+0033 "3" */
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xa, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xfe,
    0xb7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0xaa, 0xcf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfd, 0x3, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfb, 0xd, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf5, 0x7f, 0xff, 0xd8, 0x31,
    0x0, 0x15, 0xcf, 0xff, 0xc0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x2, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x1, 0x59, 0xce, 0xff, 0xdb, 0x72, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xc0,
    0x0, 0x0, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfe, 0x10, 0x0, 0x6, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x0, 0x6,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x70,
    0x0, 0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x1,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x6, 0xff, 0xd0,
    0x0, 0x0, 0xc, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xd0, 0x0, 0x0, 0x8f, 0xff, 0xfe,
    0xee, 0xee, 0xee, 0xef, 0xff, 0xfe, 0xee, 0xe4,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xa, 0xff, 0xfe, 0xee,
    0xee, 0xee, 0xee, 0xe0, 0x0, 0x0, 0xcf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xec, 0x95, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x60, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x49, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x20, 0x6f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xd0, 0x1e, 0xff,
    0xe9, 0x52, 0x0, 0x3, 0x9f, 0xff, 0xf4, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xdf, 0xfe,
    0xda, 0x50, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe, 0xc8,
    0x30, 0x0, 0x0, 0x0, 0x4, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xf3, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x83, 0x0, 0x0, 0x27, 0xa0, 0x0,
    0x0, 0x4f, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x30, 0x1, 0x69, 0xaa,
    0x85, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x12, 0xbf,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x5f, 0xff,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x5f, 0xff, 0xef, 0xfa, 0x41, 0x1, 0x5b, 0xff,
    0xfc, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x60, 0x3f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xc0, 0x1f, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0xe, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0xa, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x3, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xc0, 0x0, 0xcf,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xe5, 0x0, 0x0, 0x7, 0xff,
    0xfd, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfc, 0xbc,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xce, 0xfe, 0xc9, 0x30, 0x0, 0x0,

    /* U+0037 "7" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xfd, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf6, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x33,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x3, 0x8c, 0xef, 0xfe, 0xc8, 0x30,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x20, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xdc, 0xce, 0xff, 0xff, 0xe2, 0x0, 0x0, 0xef,
    0xff, 0x81, 0x0, 0x0, 0x29, 0xff, 0xfd, 0x0,
    0x6, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x40, 0x9, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x70, 0x9, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x7, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x50,
    0x2, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0x0, 0x0, 0x8f, 0xff, 0xb5, 0x10, 0x2,
    0x5c, 0xff, 0xf5, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x3,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x7f, 0xff, 0xfd, 0xa9, 0x9a, 0xef, 0xff,
    0xf5, 0x0, 0x6, 0xff, 0xfb, 0x20, 0x0, 0x0,
    0x4, 0xdf, 0xff, 0x50, 0xf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x5f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf3,
    0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf5, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf5, 0x5f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf3, 0xf, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xe0,
    0x7, 0xff, 0xfd, 0x50, 0x0, 0x0, 0x16, 0xef,
    0xff, 0x50, 0x0, 0x8f, 0xff, 0xff, 0xdb, 0xcd,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x5, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x5, 0x9c, 0xef, 0xfe, 0xc8, 0x40, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x3, 0x8c, 0xef, 0xec, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xdc, 0xdf, 0xff, 0xfe, 0x30, 0x0, 0x0, 0xdf,
    0xff, 0x70, 0x0, 0x0, 0x5e, 0xff, 0xe1, 0x0,
    0x6, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xfb, 0x0, 0xc, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x30, 0xf, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0xf, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0,
    0xf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xf1, 0xc, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf3, 0x6, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0xdf,
    0xff, 0xb5, 0x10, 0x14, 0xaf, 0xfe, 0xff, 0xf5,
    0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xf4, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff,
    0xfb, 0x21, 0xff, 0xf3, 0x0, 0x0, 0x1, 0x69,
    0xab, 0x97, 0x20, 0x3, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xf4, 0x0,
    0x0, 0xa, 0x72, 0x0, 0x0, 0x38, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xef, 0xfe, 0xc8, 0x30, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x7, 0xed, 0x40, 0x4f, 0xff, 0xe0, 0x6f, 0xff,
    0xf1, 0x1f, 0xff, 0xb0, 0x2, 0x87, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x87, 0x0, 0x1f, 0xff, 0xc0, 0x6f, 0xff, 0xf1,
    0x3f, 0xff, 0xe0, 0x7, 0xed, 0x40,

    /* U+003B ";" */
    0x7, 0xed, 0x40, 0x4f, 0xff, 0xe0, 0x6f, 0xff,
    0xf1, 0x1f, 0xff, 0xb0, 0x2, 0x87, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x54, 0x0, 0xd, 0xff, 0x90, 0x4f, 0xff, 0xf0,
    0x4f, 0xff, 0xf0, 0xa, 0xff, 0xc0, 0x1, 0xff,
    0x70, 0x5, 0xff, 0x20, 0x9, 0xfc, 0x0, 0xd,
    0xf7, 0x0, 0x1f, 0xf1, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0x7d, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x4, 0xaf, 0xff, 0xff, 0xc6, 0x0,
    0x0, 0x17, 0xdf, 0xff, 0xfe, 0x82, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xd6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8e, 0xff, 0xff, 0xd7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x5b, 0xff, 0xff, 0xfb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xef, 0xff, 0xfe, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34,

    /* U+003D "=" */
    0x7a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa5,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa5,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x7d, 0xff, 0xff, 0xe9, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x39, 0xff, 0xff, 0xfc, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xff, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8e, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x2, 0x8e, 0xff, 0xff, 0xd7, 0x10,
    0x0, 0x6, 0xcf, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x3a, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xf9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x27, 0xcd, 0xff, 0xec, 0x83, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x6, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xfd, 0x0, 0x5f, 0xff, 0xf8, 0x20, 0x0,
    0x6, 0xef, 0xff, 0x90, 0x9, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xf0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x55, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xb0, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8b, 0xde,
    0xff, 0xec, 0x96, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xc8,
    0x64, 0x44, 0x57, 0xae, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xe7, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0x50, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x20, 0x0, 0x5, 0xff, 0x80, 0x0,
    0x0, 0x5, 0xae, 0xff, 0xd9, 0x30, 0xd, 0xff,
    0x10, 0x1e, 0xfb, 0x0, 0x0, 0xdf, 0xd0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xdf,
    0xf1, 0x0, 0x5f, 0xf3, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x2e, 0xff, 0xfe, 0xa9, 0xbf, 0xff, 0xce,
    0xff, 0x10, 0x0, 0xdf, 0x90, 0xa, 0xfe, 0x0,
    0x0, 0xd, 0xff, 0xe4, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf1, 0x0, 0x7, 0xfe, 0x0, 0xef, 0xa0,
    0x0, 0x8, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x10, 0x0, 0x2f, 0xf2, 0x1f, 0xf6,
    0x0, 0x0, 0xef, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf1, 0x0, 0x0, 0xff, 0x54, 0xff,
    0x30, 0x0, 0x2f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0xd, 0xf7, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf1, 0x0, 0x0, 0xcf, 0x85,
    0xff, 0x10, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x10, 0x0, 0xc, 0xf7,
    0x5f, 0xf2, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf1, 0x0, 0x0, 0xdf,
    0x74, 0xff, 0x30, 0x0, 0x2f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0xf,
    0xf5, 0x1f, 0xf6, 0x0, 0x0, 0xef, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xf1, 0x0, 0x3,
    0xff, 0x20, 0xef, 0xa0, 0x0, 0x8, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x20, 0x0,
    0x8f, 0xd0, 0x9, 0xfe, 0x0, 0x0, 0xd, 0xff,
    0xe4, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf7, 0x0,
    0x3f, 0xf7, 0x0, 0x4f, 0xf6, 0x0, 0x0, 0x2e,
    0xff, 0xfd, 0xa9, 0xbe, 0xff, 0xc6, 0xff, 0xfa,
    0xaf, 0xfd, 0x0, 0x0, 0xdf, 0xd0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xc, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x5, 0xff, 0x80, 0x0,
    0x0, 0x5, 0xae, 0xff, 0xd9, 0x40, 0x0, 0x18,
    0xdf, 0xe9, 0x10, 0x0, 0x0, 0xb, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x29, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xc8, 0x64, 0x44, 0x69,
    0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9b, 0xef, 0xff,
    0xeb, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xe3, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80,
    0xbf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x10, 0x4f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfa, 0x0, 0xd, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0x0, 0x6,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x0, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50,
    0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf8, 0x0, 0x0, 0x0, 0xa, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xcb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xdf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf7, 0x0, 0x0,
    0xf, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfe, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x50, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xc0, 0x5, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf3, 0xc, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfb,

    /* U+0042 "B" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xa5,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x7f, 0xff,
    0xbb, 0xbb, 0xbb, 0xbc, 0xdf, 0xff, 0xff, 0x50,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x8f, 0xff, 0xf1, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf7, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfc, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf1, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xcf, 0xff, 0x60,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x10, 0x0, 0x7f,
    0xff, 0xbb, 0xbb, 0xbb, 0xbb, 0xcd, 0xff, 0xff,
    0xe3, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xef, 0xff, 0x20, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0x90,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xc0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xdf, 0xff, 0x50, 0x7f,
    0xff, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0xff,
    0xfa, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xda, 0x61, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x0, 0x28, 0xbe, 0xff, 0xec,
    0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0xc, 0xff, 0xff, 0xc6,
    0x20, 0x1, 0x38, 0xef, 0xff, 0xe1, 0x0, 0xc,
    0xff, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf6, 0x0, 0x8, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x46, 0x0, 0x1, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x60, 0x0, 0x0, 0xcf, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x70, 0x0, 0x1,
    0xcf, 0xff, 0xfb, 0x62, 0x0, 0x13, 0x8e, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xef, 0xfe,
    0xc9, 0x50, 0x0, 0x0,

    /* U+0044 "D" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xee, 0xee, 0xee, 0xef, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xaf, 0xff, 0xfe, 0x20, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xe1, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x30, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xa0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf3,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf5, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf6,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf6, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf5,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf3, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xa0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x30,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfa, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xd1, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x15, 0xaf,
    0xff, 0xfe, 0x20, 0x0, 0x7f, 0xff, 0xee, 0xee,
    0xee, 0xef, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x7f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xe5, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xec, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+0046 "F" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x7f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xe5, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x0, 0x27, 0xbd, 0xff, 0xed,
    0xa5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0xc, 0xff, 0xff, 0xc6,
    0x30, 0x1, 0x37, 0xcf, 0xff, 0xf3, 0x0, 0xb,
    0xff, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0xfa, 0x0, 0x7, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0x0, 0x1, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x20, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x54, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf5, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x50, 0xdf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf5,
    0x8, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x50, 0x1f, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf5, 0x0,
    0x7f, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x50, 0x0, 0xbf, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0, 0x0,
    0xcf, 0xff, 0xfc, 0x62, 0x0, 0x2, 0x6b, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xef, 0xfe,
    0xda, 0x61, 0x0, 0x0,

    /* U+0048 "H" */
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f,
    0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xef,
    0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,

    /* U+0049 "I" */
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,
    0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff,

    /* U+004A "J" */
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0xd, 0xee, 0xee, 0xee, 0xef, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x0, 0x60, 0x0, 0x0, 0x0, 0x9, 0xff, 0xd0,
    0x6, 0xfb, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x90,
    0x1f, 0xff, 0xd5, 0x0, 0x4, 0xdf, 0xff, 0x20,
    0x7, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x1, 0x6b, 0xef, 0xfd, 0x93, 0x0, 0x0,

    /* U+004B "K" */
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x40, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf5, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x60,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf7, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x5, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x5f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x4f, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x3, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x3f, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xef, 0xff, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xf4, 0xb, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x40, 0x0, 0xdf, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf4, 0x0, 0x0, 0x1e, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0x0,
    0x3, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xd0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfb, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x80, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf5, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0x30, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xe1,

    /* U+004C "L" */
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xfe, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xec, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd,

    /* U+004D "M" */
    0x7f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfd, 0x7f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0x7f, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfd, 0x7f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfd, 0x7f, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xfd, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xfd, 0x7f, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xef, 0xfd, 0x7f, 0xfd, 0xaf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x7f, 0xfd,
    0x7f, 0xfd, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf6, 0x6f, 0xfe, 0x7f, 0xfd, 0x7,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x6f, 0xfe, 0x7f, 0xfd, 0x0, 0xdf, 0xf8, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x40, 0x6f, 0xfe, 0x7f,
    0xfd, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x9f,
    0xfa, 0x0, 0x6f, 0xfe, 0x7f, 0xfd, 0x0, 0xa,
    0xff, 0xb0, 0x0, 0x3, 0xff, 0xf1, 0x0, 0x6f,
    0xfe, 0x7f, 0xfd, 0x0, 0x1, 0xff, 0xf4, 0x0,
    0xc, 0xff, 0x70, 0x0, 0x6f, 0xfe, 0x7f, 0xfd,
    0x0, 0x0, 0x7f, 0xfd, 0x0, 0x5f, 0xfd, 0x0,
    0x0, 0x6f, 0xfe, 0x7f, 0xfd, 0x0, 0x0, 0xd,
    0xff, 0x70, 0xef, 0xf4, 0x0, 0x0, 0x6f, 0xfe,
    0x7f, 0xfd, 0x0, 0x0, 0x4, 0xff, 0xf9, 0xff,
    0xb0, 0x0, 0x0, 0x6f, 0xfe, 0x7f, 0xfd, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x5f, 0xfe, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x7f,
    0xfd, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x5f, 0xfe, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x50, 0x0, 0x0, 0x0, 0x5f,
    0xfe, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfe, 0x7f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfe,

    /* U+004E "N" */
    0x7f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x7f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x7f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x7f, 0xff, 0xbf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x1d,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x7f, 0xff, 0x2, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x5f, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x8, 0xff, 0xfb, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x7f, 0xff, 0x0, 0x0, 0xbf, 0xff, 0x80,
    0x0, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0,
    0x1d, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xf0, 0x7f,
    0xff, 0x0, 0x0, 0x2, 0xff, 0xff, 0x20, 0x5,
    0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xd1, 0x5, 0xff, 0xf0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfb, 0x5, 0xff, 0xf0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x85, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xfa, 0xff, 0xf0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xf0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf0,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x0, 0x27, 0xbe, 0xff, 0xed,
    0xa5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfb, 0x62, 0x0, 0x13, 0x8e,
    0xff, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xfe, 0x10, 0x1, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x90, 0x8, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1,
    0xd, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x1f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfa, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xfe, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfe, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x1f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfa, 0xd, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf6,
    0x8, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf1, 0x1, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x0, 0x7f, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xfe, 0x10, 0x0,
    0xb, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xfb, 0x62, 0x0, 0x3, 0x7e, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xbe, 0xff, 0xfd, 0xa6, 0x10, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xc8, 0x30,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x30, 0x0, 0x7f, 0xff, 0xee, 0xee,
    0xee, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x1, 0x4a, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xd0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf4, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf9, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfc, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xc0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x2,
    0x5b, 0xff, 0xff, 0x30, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0x7f, 0xff, 0xee, 0xee, 0xee, 0xed, 0xa7, 0x20,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x0, 0x27, 0xbe, 0xff, 0xed,
    0xa5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc, 0x62,
    0x0, 0x14, 0x8e, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xfe, 0x10, 0x0, 0x1, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x90, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0,
    0xd, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfa, 0x0, 0x4f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x5f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfe, 0x0, 0x4f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x0, 0x2f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfa, 0x0, 0xe, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0,
    0x9, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf1, 0x0, 0x2, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0x10, 0x0, 0x0, 0xd, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xfa, 0x40, 0x0, 0x2,
    0x6d, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf,
    0xff, 0xff, 0xe6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x2a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xc4,
    0x10, 0x28, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x48, 0xa9, 0x84, 0x0, 0x0,

    /* U+0052 "R" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xc8, 0x30,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc3, 0x0, 0x0, 0x7f, 0xff, 0xee,
    0xee, 0xee, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x14, 0xaf, 0xff,
    0xf4, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xd0, 0x7, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x40, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf9, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0x7, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0x7, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xd0,
    0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x25, 0xbf,
    0xff, 0xf3, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x7f, 0xff, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xd0,
    0x0, 0x0, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0x7,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfe,
    0x10, 0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfa, 0x0, 0x7, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf6, 0x0, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xf2, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xc0,

    /* U+0053 "S" */
    0x0, 0x0, 0x3, 0x8c, 0xdf, 0xfe, 0xc9, 0x50,
    0x0, 0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x10, 0x0, 0x5f, 0xff, 0xff, 0xfe,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x2f, 0xff, 0xf8,
    0x20, 0x0, 0x2, 0x6b, 0xff, 0x20, 0x9, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70, 0x0,
    0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf9, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xd9, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xcf, 0xff, 0xff,
    0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x8c, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x6d, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfb, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x90, 0xde, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0x5f, 0xff,
    0xe8, 0x40, 0x0, 0x0, 0x39, 0xff, 0xfc, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xde, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x3, 0x7b, 0xde, 0xfe,
    0xdb, 0x72, 0x0, 0x0,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xce, 0xee, 0xee, 0xee,
    0xff, 0xff, 0xee, 0xee, 0xee, 0xec, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf8, 0xaf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x8a,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf8, 0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x8a, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8, 0xaf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x8a, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0xaf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x8a, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf8, 0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8, 0xaf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x8a, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf7, 0x9f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x68, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf5,
    0x5f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x31, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf0, 0xb, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf9, 0x0,
    0x4f, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0x20, 0x0, 0x9f, 0xff, 0xe8, 0x30, 0x0,
    0x38, 0xff, 0xff, 0x70, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xad, 0xef, 0xed,
    0xa5, 0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0xd, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf1, 0x6, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x90, 0x0, 0xef, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0,
    0x8f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x1f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf4,
    0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0x0, 0x3,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x10, 0x0, 0x0, 0xaf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xe0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x0, 0x6f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x30, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xa4, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xb0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x10, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xb0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xe9, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf6, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x10, 0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x40, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xb0, 0x0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x3, 0xff, 0xe0, 0x9, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xef, 0xf6, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0,
    0x3f, 0xff, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0xe,
    0xff, 0x40, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x3, 0xff, 0xe0, 0x0, 0x9, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x3f, 0xff, 0x0, 0x0, 0x3f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf0, 0x0, 0xe, 0xff,
    0x30, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x8, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x50,
    0x4, 0xff, 0xe0, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x0, 0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x0, 0x3f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf0, 0xe, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xdf, 0xf5, 0x8, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x54,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfa, 0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xa, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf7, 0x0, 0xe, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xfb, 0x0, 0x0,
    0x3f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfe, 0x10, 0x0, 0x0, 0x8f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x20, 0x0, 0x0, 0x2f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf8, 0x0, 0x8, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf4, 0x3, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xe2, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf7, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf6, 0x9, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xfa, 0x0, 0xd, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0x10, 0x0, 0x2f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x40, 0x0, 0x0, 0x6f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0x30, 0x0, 0x0, 0x1e,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xfd,
    0x0, 0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf9, 0x0, 0x7, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf5,
    0x3, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf2,

    /* U+0059 "Y" */
    0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x30,
    0x0, 0xaf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfa, 0x0, 0x0, 0x1f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x0,
    0x0, 0x6, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x2,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf9, 0x0, 0x0, 0x5f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x30, 0x0, 0xef, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xc0, 0x9, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x3f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x4e, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xe5, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5,

    /* U+005B "[" */
    0x7f, 0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xfa,
    0x7f, 0xff, 0xaa, 0xa7, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0x7f, 0xff, 0xaa, 0xa7,
    0x7f, 0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xfa,

    /* U+005C "\\" */
    0x18, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb,

    /* U+005D "]" */
    0x5f, 0xff, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xfc,
    0x3a, 0xaa, 0xdf, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x8f, 0xfc, 0x3a, 0xaa, 0xdf, 0xfc,
    0x5f, 0xff, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xfc,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0xbf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x4f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xb0, 0xe, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x40, 0x7, 0xff, 0x10, 0x0,
    0x0, 0xa, 0xfe, 0x0, 0x1, 0xff, 0x70, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x0, 0xaf, 0xe0, 0x0,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0xc, 0xfc, 0x0,
    0x6, 0xff, 0x30, 0x0, 0x0, 0x6, 0xff, 0x30,
    0xc, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf1,

    /* U+005F "_" */
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0,

    /* U+0060 "`" */
    0x5, 0x88, 0x83, 0x0, 0x0, 0x0, 0xbf, 0xfe,
    0x30, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0x40, 0x0, 0x0, 0x2, 0xdf,
    0xf4,

    /* U+0061 "a" */
    0x0, 0x0, 0x59, 0xde, 0xff, 0xda, 0x40, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc2,
    0x0, 0x9, 0xff, 0xff, 0xfd, 0xde, 0xff, 0xff,
    0xd1, 0x0, 0x2f, 0xf9, 0x30, 0x0, 0x2, 0xaf,
    0xff, 0x90, 0x0, 0x51, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x50, 0x0, 0x0, 0x14, 0x45,
    0x55, 0x55, 0xef, 0xf5, 0x0, 0x8, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xb, 0xff, 0xe7,
    0x20, 0x0, 0x0, 0xe, 0xff, 0x61, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf6, 0x4f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x63, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6, 0xf,
    0xff, 0x90, 0x0, 0x0, 0x6, 0xff, 0xff, 0x60,
    0x7f, 0xff, 0xd7, 0x55, 0x7d, 0xff, 0xff, 0xf6,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x6b, 0xff,
    0x60, 0x0, 0x29, 0xdf, 0xfe, 0xc8, 0x20, 0xbf,
    0xf6,

    /* U+0062 "b" */
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x50, 0x6, 0xbe, 0xff,
    0xd9, 0x40, 0x0, 0x0, 0xef, 0xf5, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0x0, 0xe, 0xff, 0xaf,
    0xff, 0xfe, 0xdf, 0xff, 0xff, 0xe3, 0x0, 0xef,
    0xff, 0xff, 0x82, 0x0, 0x3, 0xaf, 0xff, 0xe1,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xa0, 0xef, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x2e, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf7, 0xef, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xae, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xbe, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfa, 0xef, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x7e, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf2, 0xef, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfa, 0xe,
    0xff, 0xff, 0xf8, 0x20, 0x0, 0x3a, 0xff, 0xfe,
    0x10, 0xef, 0xf8, 0xff, 0xff, 0xed, 0xff, 0xff,
    0xfe, 0x30, 0xe, 0xff, 0x34, 0xef, 0xff, 0xff,
    0xff, 0xfb, 0x10, 0x0, 0xef, 0xf3, 0x0, 0x6b,
    0xef, 0xfd, 0x94, 0x0, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfe, 0xef,
    0xff, 0xff, 0x40, 0x0, 0xbf, 0xff, 0xc4, 0x0,
    0x1, 0x6e, 0xff, 0xe1, 0x6, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x40, 0xe, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x4f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x6, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xde, 0x40, 0x0, 0xaf,
    0xff, 0xc4, 0x0, 0x0, 0x6e, 0xff, 0xe1, 0x0,
    0xb, 0xff, 0xff, 0xfd, 0xef, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0xa4,
    0x0, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0x0, 0x0,
    0x2, 0x8c, 0xff, 0xec, 0x71, 0x2, 0xff, 0xf1,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x62,
    0xff, 0xf1, 0x0, 0x1d, 0xff, 0xff, 0xfd, 0xef,
    0xff, 0xfa, 0xff, 0xf1, 0x0, 0xcf, 0xff, 0xc4,
    0x0, 0x1, 0x7e, 0xff, 0xff, 0xf1, 0x8, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xf1,
    0xe, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf1, 0x4f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf1, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x8f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf1,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf1, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf1, 0x4f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf1, 0xe, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf1,
    0x7, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf1, 0x0, 0xcf, 0xff, 0x91, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xf1, 0x0, 0x1d, 0xff, 0xff,
    0xca, 0xbd, 0xff, 0xfa, 0xff, 0xf1, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff, 0xf1,
    0x0, 0x0, 0x2, 0x8c, 0xff, 0xec, 0x82, 0x0,
    0xff, 0xf1,

    /* U+0065 "e" */
    0x0, 0x0, 0x2, 0x8c, 0xff, 0xec, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xcb,
    0xdf, 0xff, 0xfc, 0x0, 0x0, 0xb, 0xff, 0xf7,
    0x0, 0x0, 0x18, 0xff, 0xf9, 0x0, 0x7, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4, 0x0,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xb0, 0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x17, 0xff, 0xd5, 0x55, 0x55, 0x55,
    0x55, 0x55, 0xef, 0xf4, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x58, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x7f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x3, 0xb0, 0x0, 0x0, 0xaf, 0xff,
    0xc4, 0x0, 0x0, 0x29, 0xff, 0xa0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xed, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x17, 0xbe, 0xff, 0xeb,
    0x71, 0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x6b, 0xef, 0xeb, 0x50, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xff, 0xdb, 0xcf, 0x90, 0x0, 0x1, 0xff,
    0xf7, 0x0, 0x1, 0x10, 0x0, 0x5, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x5a,
    0xac, 0xff, 0xfa, 0xaa, 0xaa, 0x0, 0x0, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xec, 0x82, 0x0,
    0xcf, 0xf5, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xcf, 0xf5, 0x0, 0x1d, 0xff, 0xff,
    0xfd, 0xdf, 0xff, 0xfb, 0xcf, 0xf5, 0x0, 0xcf,
    0xff, 0xb4, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xf5,
    0x7, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf5, 0xe, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf5, 0x4f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf5,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf5, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf5, 0x3f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf5,
    0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x6, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x2, 0xcf, 0xff, 0xf5, 0x0, 0xaf, 0xff, 0xe7,
    0x20, 0x13, 0x8e, 0xff, 0xff, 0xf5, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xf5,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x59, 0xcc, 0xba,
    0x50, 0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xc0,
    0x0, 0x6a, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x60, 0x1, 0xff, 0xfa, 0x41, 0x0, 0x0,
    0x28, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xed, 0xdf, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x0, 0x0, 0x16, 0x9c, 0xef, 0xfe, 0xc8, 0x30,
    0x0, 0x0,

    /* U+0068 "h" */
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf5, 0x1, 0x7b, 0xef, 0xfd, 0x92, 0x0, 0x0,
    0xef, 0xf5, 0x6e, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0xef, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xf9, 0x0, 0xef, 0xff, 0xfe, 0x61, 0x0, 0x16,
    0xef, 0xff, 0x40, 0xef, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xb0, 0xef, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf0, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf1, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0xef,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf3,

    /* U+0069 "i" */
    0x2, 0x75, 0x2, 0xff, 0xf9, 0x6f, 0xff, 0xe3,
    0xff, 0xfa, 0x5, 0xa8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0xef,
    0xf5, 0xe, 0xff, 0x50, 0xef, 0xf5, 0xe, 0xff,
    0x50, 0xef, 0xf5, 0xe, 0xff, 0x50, 0xef, 0xf5,
    0xe, 0xff, 0x50, 0xef, 0xf5, 0xe, 0xff, 0x50,
    0xef, 0xf5, 0xe, 0xff, 0x50, 0xef, 0xf5, 0xe,
    0xff, 0x50, 0xef, 0xf5, 0xe, 0xff, 0x50, 0xef,
    0xf5,

    /* U+006A "j" */
    0x0, 0x0, 0x0, 0x1, 0x66, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x3, 0xa9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x60, 0x1, 0x40,
    0x0, 0x9f, 0xff, 0x20, 0x8, 0xfe, 0xce, 0xff,
    0xfa, 0x0, 0xe, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x4, 0xbe, 0xff, 0xc7, 0x0, 0x0,

    /* U+006B "k" */
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf9,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x90, 0x0, 0xef, 0xf5, 0x0, 0x0, 0xa, 0xff,
    0xf8, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0xbf,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x1c,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x1,
    0xdf, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x2e, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf9, 0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xfe, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xfe, 0x33, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0xef, 0xff, 0xe2, 0x0, 0x6f, 0xff,
    0xb0, 0x0, 0x0, 0xef, 0xfd, 0x20, 0x0, 0x9,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x50, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xf2, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfd, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xa0,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf7,

    /* U+006C "l" */
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5, 0xef, 0xf5,
    0xef, 0xf5,

    /* U+006D "m" */
    0xef, 0xf3, 0x2, 0x8c, 0xef, 0xec, 0x61, 0x0,
    0x0, 0x27, 0xce, 0xfe, 0xc8, 0x20, 0x0, 0xe,
    0xff, 0x38, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0xef,
    0xfc, 0xff, 0xfd, 0xbc, 0xff, 0xff, 0xf3, 0xbf,
    0xff, 0xec, 0xce, 0xff, 0xff, 0x70, 0xe, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x4, 0xef, 0xff, 0x10, 0xef, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x2, 0xff, 0xf8, 0xe, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xc0, 0xef, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0xe, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0,

    /* U+006E "n" */
    0xef, 0xf3, 0x1, 0x7c, 0xef, 0xfd, 0x92, 0x0,
    0x0, 0xef, 0xf3, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xef, 0xfc, 0xff, 0xfe, 0xcc, 0xef,
    0xff, 0xf9, 0x0, 0xef, 0xff, 0xfc, 0x30, 0x0,
    0x3, 0xdf, 0xff, 0x40, 0xef, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xb0, 0xef, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf0, 0xef, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf1, 0xef,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf3,

    /* U+006F "o" */
    0x0, 0x0, 0x1, 0x7c, 0xef, 0xfd, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xfd, 0xef, 0xff, 0xff, 0x70, 0x0, 0x0, 0xbf,
    0xff, 0xc4, 0x0, 0x1, 0x6e, 0xff, 0xf4, 0x0,
    0x7, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0x10, 0xe, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x80, 0x4f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xd0, 0x7f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf2, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf1, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x3f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xd0,
    0xe, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x70, 0x6, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfe, 0x10, 0x0, 0xaf, 0xff, 0xc4,
    0x0, 0x1, 0x6e, 0xff, 0xf4, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfd, 0xef, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd,
    0xa5, 0x0, 0x0, 0x0,

    /* U+0070 "p" */
    0xef, 0xf3, 0x1, 0x7b, 0xef, 0xfd, 0x94, 0x0,
    0x0, 0xe, 0xff, 0x36, 0xef, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0xef, 0xfa, 0xff, 0xfe, 0xcb,
    0xcf, 0xff, 0xfe, 0x30, 0xe, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x17, 0xff, 0xfe, 0x10, 0xef, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfa, 0xe,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf2, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x7e, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xbe, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfb, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xae, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf7, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x2e, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xa0, 0xef, 0xff, 0xff,
    0x82, 0x0, 0x3, 0xaf, 0xff, 0xe1, 0xe, 0xff,
    0xaf, 0xff, 0xfe, 0xdf, 0xff, 0xff, 0xe3, 0x0,
    0xef, 0xf5, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xb1,
    0x0, 0xe, 0xff, 0x50, 0x6, 0xbe, 0xff, 0xd9,
    0x40, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x2, 0x8c, 0xff, 0xec, 0x71, 0x0,
    0xff, 0xf1, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x60, 0xff, 0xf1, 0x0, 0x1d, 0xff, 0xff,
    0xfd, 0xef, 0xff, 0xf8, 0xff, 0xf1, 0x0, 0xcf,
    0xff, 0xc4, 0x0, 0x1, 0x7f, 0xff, 0xff, 0xf1,
    0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x2, 0xdf,
    0xff, 0xf1, 0xe, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xf1, 0x4f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x7f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf1,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf1, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf1, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x4f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1,
    0xe, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf1, 0x7, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xf1, 0x0, 0xcf, 0xff, 0xc4,
    0x0, 0x1, 0x6e, 0xff, 0xff, 0xf1, 0x0, 0x1d,
    0xff, 0xff, 0xfd, 0xef, 0xff, 0xf9, 0xff, 0xf1,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x52,
    0xff, 0xf1, 0x0, 0x0, 0x2, 0x8c, 0xff, 0xec,
    0x71, 0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf1,

    /* U+0072 "r" */
    0xef, 0xf3, 0x1, 0x7b, 0xec, 0xef, 0xf3, 0x5e,
    0xff, 0xfc, 0xef, 0xf7, 0xff, 0xff, 0xfc, 0xef,
    0xff, 0xff, 0x94, 0x21, 0xef, 0xff, 0xe2, 0x0,
    0x0, 0xef, 0xff, 0x40, 0x0, 0x0, 0xef, 0xfc,
    0x0, 0x0, 0x0, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x28, 0xce, 0xff, 0xeb, 0x84, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0xaf, 0xff, 0xfe, 0xcc, 0xef, 0xff,
    0xe0, 0x0, 0x3f, 0xff, 0x92, 0x0, 0x0, 0x16,
    0xd6, 0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x85,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xff,
    0xff, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x18, 0xdf,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x69, 0xdf, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0x6,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x6,
    0xfd, 0x73, 0x0, 0x0, 0x5, 0xef, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xdc, 0xdf, 0xff, 0xff, 0x20,
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x38, 0xbe, 0xff, 0xfd, 0xa4, 0x0,
    0x0,

    /* U+0074 "t" */
    0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x5a, 0xac, 0xff, 0xfa, 0xaa, 0xaa,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfa, 0x0, 0x3, 0x20, 0x0, 0x0, 0xbf,
    0xff, 0xec, 0xdf, 0xa0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x7c, 0xff,
    0xea, 0x30,

    /* U+0075 "u" */
    0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfe, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfe, 0x1f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0x1f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xfe, 0x1f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x1f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x1f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe,
    0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfe, 0xf, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfe, 0xf, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xd, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfe, 0x9, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xfe, 0x2, 0xff,
    0xfe, 0x40, 0x0, 0x2, 0xbf, 0xff, 0xfe, 0x0,
    0x7f, 0xff, 0xfe, 0xbb, 0xdf, 0xff, 0xcf, 0xfe,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x3f,
    0xfe, 0x0, 0x0, 0x17, 0xce, 0xfe, 0xc8, 0x20,
    0x3f, 0xfe,

    /* U+0076 "v" */
    0xd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfd, 0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x60, 0x0, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0,
    0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf8, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x10, 0x0, 0x0, 0xaf, 0xfa,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0, 0x0,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfe,
    0x0, 0x0, 0xd, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf6, 0x0, 0x5, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0xcf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x40, 0x3f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfa, 0xa, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf3, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x23,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0xd,
    0xff, 0x30, 0x0, 0x0, 0x0, 0xf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x7f,
    0xf9, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x10, 0x2, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xbf, 0xfd, 0xff, 0x60,
    0x0, 0x0, 0x3, 0xff, 0xa0, 0x0, 0xc, 0xff,
    0x40, 0x0, 0x0, 0x2f, 0xfd, 0x4f, 0xfc, 0x0,
    0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x6f, 0xfa,
    0x0, 0x0, 0x8, 0xff, 0x70, 0xef, 0xf2, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0x0, 0x0, 0xdf, 0xf1, 0x8, 0xff, 0x80, 0x0,
    0x5, 0xff, 0x80, 0x0, 0x0, 0xa, 0xff, 0x60,
    0x0, 0x4f, 0xfb, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0xbf, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0x0,
    0xa, 0xff, 0x40, 0x0, 0xbf, 0xf3, 0x0, 0x1f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2, 0x0,
    0xff, 0xe0, 0x0, 0x5, 0xff, 0x90, 0x7, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x8, 0xff, 0x70, 0x6f,
    0xf8, 0x0, 0x0, 0xf, 0xff, 0x0, 0xdf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0xc, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xf5, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6, 0xff, 0xc0,
    0x0, 0x0, 0x3, 0xff, 0xb9, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xc0, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xf8, 0x0, 0x1e, 0xff, 0x90, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xbf, 0xfd, 0x0, 0x2f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfa, 0xc, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfd, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x7f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x8f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x80,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x1, 0xef, 0xf9, 0x0, 0x0, 0x9,
    0xff, 0xe1, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0,
    0x5, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf2, 0x2, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0,

    /* U+0079 "y" */
    0xd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfd, 0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x60, 0x0, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf8, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0,
    0x3, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x90, 0x0, 0x0,
    0x5f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf7, 0x0, 0x3, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0xaf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x40, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfb, 0x8, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf3, 0xef, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xdf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa7, 0x0, 0x3,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xcd, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xff, 0xc7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4a, 0xaa, 0xaa, 0xaa, 0xaa, 0xbf, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xa4,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+007B "{" */
    0x0, 0x0, 0x2, 0x9e, 0xff, 0x40, 0x0, 0x3,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0xdf, 0xff, 0xda,
    0x20, 0x0, 0x2f, 0xff, 0x80, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x5f, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0xfe,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xb0, 0x0, 0x1, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x1f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x5f, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xfd, 0xa2, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x2a, 0xef, 0xf4,

    /* U+007C "|" */
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,
    0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9, 0x7f, 0xf9,

    /* U+007D "}" */
    0x5f, 0xfd, 0x91, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x20, 0x0, 0x3a, 0xdf, 0xff, 0xc0, 0x0, 0x0,
    0x9, 0xff, 0xf1, 0x0, 0x0, 0x1, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x0, 0x0, 0x9f,
    0xff, 0xba, 0x0, 0x0, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xff, 0xf5, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0x0, 0x0, 0x8, 0xff, 0xf2,
    0x0, 0x3a, 0xdf, 0xff, 0xd0, 0x0, 0x5f, 0xff,
    0xff, 0x30, 0x0, 0x5f, 0xfe, 0x92, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x8, 0xef, 0xd7, 0x0, 0x0, 0x0, 0x6f,
    0xc0, 0xb, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x9,
    0xfa, 0x5, 0xff, 0xd8, 0xbf, 0xfe, 0x30, 0x1,
    0xff, 0x70, 0xbf, 0xc0, 0x0, 0x6f, 0xff, 0xa8,
    0xef, 0xf2, 0xe, 0xf5, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xf7, 0x0, 0xcc, 0x20, 0x0, 0x0, 0x19,
    0xef, 0xd6, 0x0,

    /* U+00B0 "°" */
    0x0, 0x5, 0xbe, 0xfc, 0x60, 0x0, 0x0, 0xaf,
    0xfd, 0xdf, 0xfc, 0x10, 0x8, 0xfc, 0x20, 0x1,
    0xaf, 0xc0, 0x1f, 0xe1, 0x0, 0x0, 0xc, 0xf4,
    0x5f, 0x90, 0x0, 0x0, 0x5, 0xf9, 0x6f, 0x70,
    0x0, 0x0, 0x3, 0xfa, 0x5f, 0x90, 0x0, 0x0,
    0x5, 0xf9, 0x1f, 0xe1, 0x0, 0x0, 0xc, 0xf4,
    0x8, 0xfc, 0x20, 0x1, 0xbf, 0xc0, 0x0, 0xaf,
    0xfe, 0xdf, 0xfd, 0x10, 0x0, 0x5, 0xbe, 0xfc,
    0x70, 0x0,

    /* U+2022 "•" */
    0x0, 0x1, 0x0, 0x0, 0x7f, 0xfd, 0x30, 0x5f,
    0xff, 0xfe, 0x1a, 0xff, 0xff, 0xf4, 0xaf, 0xff,
    0xff, 0x44, 0xff, 0xff, 0xd0, 0x5, 0xdf, 0xb2,
    0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xbb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc8, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa6, 0x10, 0x1f,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x84, 0x0, 0x0,
    0x1, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x89, 0x87,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x24, 0x54, 0x6f, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x6, 0xdf, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xcd, 0xdb, 0x72, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xef, 0xff, 0xea,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x17, 0x10, 0x0, 0x68, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x83, 0x0, 0x1,
    0x71, 0xdf, 0x20, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x2, 0xfd, 0xff, 0x96, 0x66, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x66, 0x69, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0xcc, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xcc, 0xce, 0xff, 0xff, 0x20, 0x0,
    0xdf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x2, 0xff, 0xff, 0x20,
    0x0, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x2, 0xff, 0xff,
    0x20, 0x0, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x2, 0xff,
    0xff, 0x40, 0x0, 0xdf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x62, 0x22, 0xef, 0xfc,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x56, 0xef,
    0xfb, 0x22, 0x26, 0xff, 0xff, 0x20, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x2, 0xff, 0xff, 0x20, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x2, 0xff, 0xff, 0x20,
    0x0, 0xcf, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xde, 0xff, 0xf8, 0x0, 0x2, 0xff, 0xff,
    0xca, 0xaa, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0xaa, 0xac, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb8, 0x88, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfe, 0x88,
    0x8b, 0xff, 0xff, 0x20, 0x0, 0xcf, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x0, 0x2, 0xff, 0xff, 0x20, 0x0, 0xcf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf7, 0x0, 0x2, 0xff, 0xff, 0x20, 0x0, 0xcf,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf7, 0x0, 0x2, 0xff, 0xff, 0x74, 0x44,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfc, 0x44, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xef, 0xff,
    0xff, 0x30, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x3,
    0xff, 0x7f, 0x20, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x2, 0xf7,

    /* U+F00B "" */
    0x28, 0x88, 0x88, 0x88, 0x71, 0x0, 0x28, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x6e, 0xff, 0xff, 0xff, 0xe5, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x17, 0x88, 0x88, 0x88, 0x71,
    0x0, 0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0x77, 0x77,
    0x77, 0x60, 0x0, 0x16, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x61, 0xcf, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x1c, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x2, 0xef, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x4b, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x26, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0x40, 0x0, 0x4, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xfa, 0x0,
    0x4f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xa0, 0xdf, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xf3,
    0xef, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xc0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x60, 0x2e, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xd1, 0x8f, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x60, 0xcf, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf5, 0xaf, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xe1,
    0xb, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0x30, 0x0, 0x9e, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xc3, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0xdd, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x30,
    0x0, 0x0, 0xef, 0xff, 0xf7, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xfb, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x0, 0x3,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0xef, 0xff, 0xf7, 0x0,
    0xc, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xd0, 0x0, 0xef, 0xff, 0xf7,
    0x0, 0x4f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xef, 0xff,
    0xf7, 0x0, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x40, 0x0, 0xef,
    0xff, 0xf7, 0x0, 0xa, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0xef, 0xff, 0xf7, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xef, 0xff, 0xff, 0x40, 0x0,
    0x0, 0xef, 0xff, 0xf7, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf7, 0x0, 0x5, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf7, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xfe, 0x0, 0xb, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0x80, 0x3f, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xc0, 0x5f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xf0, 0x7f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf0, 0x6f, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xf0, 0x5f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x44, 0x10, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xe0, 0x2f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0xa, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x40, 0x5,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x29, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xb9, 0x78, 0x9d, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0xbc, 0xdd, 0xca, 0x73, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xff, 0x92, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x6, 0xfb, 0x20, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x4, 0xdf, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xf9, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xaf, 0xff, 0xe2, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x20,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xd4, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x4, 0xdf, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0x0,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa7,
    0x7b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0xc, 0xff, 0xa1, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x2b, 0xff, 0xa0, 0x0,
    0x0, 0x1, 0x93, 0x0, 0x3, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x20, 0x0, 0x59, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xab,
    0xba, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x9a, 0x50, 0x0, 0x0, 0x7, 0xbb, 0xba,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0xef, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0xe, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0xef, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xe, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xf6, 0x4e, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xe3, 0x0, 0x1c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xa0, 0x2, 0xdf, 0x40, 0x7,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x70, 0x4,
    0xff, 0xff, 0x70, 0x4, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0x50, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x2,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfd, 0x20, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xc1, 0x1, 0xbf, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xfc, 0x10,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x9f, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xf9, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x6f, 0xff, 0xff, 0xf5,
    0x0, 0x5f, 0xff, 0xff, 0xf6, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3e, 0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xe4,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x2d, 0xff, 0xff, 0xf2,
    0x5f, 0xff, 0xd2, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0xb, 0xff, 0xf9, 0x0, 0x9f, 0xb0, 0x1, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x8, 0xfc, 0x0, 0x0,
    0x40, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x88, 0x88, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x88,
    0x88, 0x88, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xac, 0xcc, 0xcc, 0xdf, 0xff, 0xff, 0xff, 0xfd,
    0xcc, 0xcc, 0xca, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0xcf, 0xff,
    0xfc, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22, 0x10,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0xb,
    0xff, 0xc0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x99, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x66, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x8, 0xfa, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x6, 0xf8, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xdf, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xa0, 0x0, 0x2, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0x50, 0x0, 0xcf,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xfe, 0x10, 0x6f, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xfa, 0xd, 0xff, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa8, 0x88, 0x88, 0x88, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdd,
    0xda, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7b,
    0xef, 0xff, 0xfd, 0x95, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x20, 0x0,
    0xa, 0xff, 0xff, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x9, 0xff, 0xff, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0x8, 0xff, 0xff, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xaa, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0x68, 0xff, 0xff, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xe, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x8f, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xa9, 0x87, 0x76, 0xbf,
    0xff, 0xff, 0xff, 0x7, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xaa, 0x80, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x80, 0xff,
    0xff, 0xff, 0xfd, 0xdd, 0xef, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfb,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xf3, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x20, 0x0, 0x0, 0x0, 0x2, 0x9f, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0xff, 0xff, 0x9e, 0xff,
    0xff, 0xff, 0xfb, 0x63, 0x11, 0x36, 0xaf, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0xff, 0xff, 0x81,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xff, 0xff,
    0x90, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xa0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x29, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x3,
    0x79, 0xba, 0x98, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x43, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xab, 0xbb, 0xbb, 0xbd, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x7f, 0xa1, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xef,
    0xfd, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x9f, 0xff, 0xa0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x8, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xef,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xcf, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x1, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x2d, 0xff, 0xe0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xdf, 0xff, 0x60, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xdf,
    0xf8, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x29, 0x40, 0x0, 0x3a,
    0xbb, 0xbb, 0xbb, 0xdf, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x98, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x2, 0x50, 0x0,
    0x5, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x1, 0xff, 0xd3, 0x0, 0x7, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf4, 0x0,
    0xb, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf2, 0x0, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xd0, 0x0,
    0x8f, 0xfe, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x70, 0x1, 0xff, 0xf4, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x8, 0xfb, 0x10, 0x0, 0xaf, 0xfe, 0x0, 0xb,
    0xff, 0x90, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xfe, 0x10, 0x2,
    0xff, 0xf4, 0x0, 0x6f, 0xfd, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x9,
    0xff, 0xfa, 0x0, 0xc, 0xff, 0x80, 0x3, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x7, 0xff, 0xf1, 0x0, 0x8f,
    0xfb, 0x0, 0xf, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xe,
    0xff, 0x50, 0x6, 0xff, 0xd0, 0x0, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xcf, 0xf6, 0x0, 0x5f, 0xfd,
    0x0, 0xf, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x2f, 0xff,
    0x30, 0x6, 0xff, 0xc0, 0x0, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x2d, 0xff, 0xe0, 0x0, 0xaf, 0xfa, 0x0,
    0x2f, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xd, 0xff, 0xf5, 0x0,
    0xe, 0xff, 0x70, 0x5, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xdf, 0xf7, 0x0, 0x6, 0xff, 0xf2, 0x0, 0x9f,
    0xfb, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x2, 0x83, 0x0, 0x1, 0xef,
    0xfb, 0x0, 0xe, 0xff, 0x70, 0x3a, 0xbb, 0xbb,
    0xbb, 0xdf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0x30, 0x5, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0x80,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xb0, 0x0, 0x6f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xa0, 0x0, 0x1e,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x9d,
    0x60, 0x0, 0xd, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x2, 0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,
    0x20, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x31,
    0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc4, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x75, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x3f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x3, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0x74,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x47, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x7, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x70,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xd9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0x40, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xef, 0xff, 0x50, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xcf, 0xff, 0x80, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x9f, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x4f, 0xff, 0xf6, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xd, 0xff, 0xff, 0x30,
    0x7, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x5, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xb5, 0x10, 0x9f, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xcf, 0xff, 0xff, 0xd9, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5b, 0xbb, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8b, 0x50, 0xbf, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf4, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf7, 0xcf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xf8, 0xcf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff,
    0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xf0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xcf, 0xff, 0xf0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xcf, 0xff, 0xf2, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf,
    0xff, 0xf0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xf8, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf7, 0xcf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xf6, 0xaf, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xab, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x9b, 0xbb, 0xbb, 0xba, 0x81, 0x0, 0x0,
    0x0, 0x29, 0xbb, 0xbb, 0xbb, 0xa8, 0x10, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x8, 0xef, 0xff, 0xff, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xff,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04D "" */
    0x2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa8, 0x10, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x8, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xb7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbb, 0xb5, 0x5f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xfb, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfb, 0x8f,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x1f, 0xff, 0xfb,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x1f, 0xff,
    0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x1f, 0xff, 0xfb, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf,
    0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x2f, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x1f, 0xff, 0xfb, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x1f,
    0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x1f, 0xff, 0xfb,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xfb, 0x8f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfb, 0x8f, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfb, 0x7f,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xfb, 0xb, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x73, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xc4, 0x0,

    /* U+F054 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xba, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xa9,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xa1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x4, 0x67,
    0x77, 0x77, 0x77, 0x79, 0xff, 0xff, 0xff, 0x77,
    0x77, 0x77, 0x77, 0x76, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F068 "" */
    0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x4, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x20,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x23, 0x44, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xd9,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xfe, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xf8, 0x30, 0x0,
    0x2, 0x7e, 0xff, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x4f, 0xff, 0xa2, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf6,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xd0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x20, 0x4, 0xef,
    0xff, 0xff, 0xff, 0x40, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xcf, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x2f, 0xde, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xaf, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x9, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x10, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x1, 0x69, 0x97, 0x20, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfd, 0x95, 0x33, 0x47,
    0xcf, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0xad,
    0xef, 0xfe, 0xdb, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x2, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x44, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x1, 0x59, 0xdf, 0xff, 0xff, 0xff,
    0xda, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x90, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x51, 0x0, 0x2,
    0x6d, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xfe, 0x30, 0x2e, 0xfd,
    0x92, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x60, 0xef, 0xff, 0xf5, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xb1, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe3, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x6, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x90, 0x7,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xb1, 0xdf, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xfe,
    0x95, 0x33, 0x51, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36,
    0xac, 0xef, 0xfe, 0xdb, 0x81, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20,
    0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x43, 0x33, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x71, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa4, 0x38, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x1, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x2, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x20, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x68, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xc1, 0x0, 0x35, 0x55, 0x55, 0x55,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x55,
    0x5a, 0xff, 0xff, 0xfc, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xc0, 0x3, 0xff, 0xff, 0xff, 0xc0, 0x8, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfd, 0x10, 0x3f, 0xff, 0xff, 0xfd, 0x10, 0x8,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe2, 0x2, 0xef, 0xff, 0xff, 0xe1, 0x0,
    0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x30, 0x1e, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x68, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0x50, 0xb, 0x70, 0x0, 0x5, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf7, 0x0, 0xaf, 0xf6, 0x0, 0x8, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x80, 0x9, 0xff, 0xff, 0x50, 0x8,
    0xff, 0xff, 0xc1, 0x0, 0x35, 0x55, 0x55, 0x9f,
    0xff, 0xff, 0xf9, 0x0, 0x6f, 0xff, 0xff, 0xf6,
    0x5a, 0xff, 0xff, 0xfc, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xcf, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xae, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xf9, 0x2,
    0xef, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0x90, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf6, 0x0, 0x1d, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0x60, 0x9f, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xf2, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf1,
    0x1c, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x50, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x38, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x70, 0x0, 0x5,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfc, 0x0, 0x4f, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xc0, 0xaf, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xf2, 0x5f, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xd0, 0x7, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x7f, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x9f,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x29, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x59, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x14, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x30, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x5d, 0xff, 0xf6, 0xbf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf6, 0xd, 0xff, 0xf6, 0xc, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xae, 0x60, 0xd,
    0xff, 0xf6, 0x1, 0xbd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x31, 0x0, 0x5f, 0xff, 0xe0, 0x0, 0x31, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x50,
    0x5f, 0xff, 0xe0, 0xa, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xf4, 0x5f, 0xff,
    0xe0, 0xaf, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0x8f, 0xff, 0xe9, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf9,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0xa, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0,
    0x0, 0x0,

    /* U+F07B "" */
    0x1, 0x57, 0x88, 0x88, 0x88, 0x88, 0x87, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x31, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x70,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x46, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x33, 0x33, 0x4f, 0xff, 0xff, 0xff, 0xf8,
    0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x22, 0x22, 0x22, 0x10, 0x1f, 0xff, 0xff,
    0xff, 0xf6, 0x1, 0x22, 0x22, 0x22, 0x22, 0x10,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xf, 0xff,
    0xff, 0xff, 0xf5, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x5,
    0xbc, 0xcc, 0xcb, 0x80, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd7, 0x55, 0x55, 0x55, 0x7d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x8, 0xfa, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x6, 0xf8, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xdf, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0x84,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xd9, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8e, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x2, 0xcf, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x5e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xae, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xfc,
    0xa6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x32,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x15, 0x64, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xbf, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x53, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0x70, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x90, 0x8f, 0xff,
    0xff, 0xef, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xf6, 0xd, 0xff, 0xfd,
    0x10, 0x3f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0xff, 0xff, 0x50,
    0x0, 0x9f, 0xff, 0xb0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xf, 0xff, 0xf6, 0x0,
    0xa, 0xff, 0xfb, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xcf, 0xff, 0xe5, 0x27,
    0xff, 0xff, 0x70, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xbf, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x56, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x8f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xf8, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xd1,
    0x3, 0xff, 0xff, 0x80, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xf, 0xff, 0xf5, 0x0,
    0x9, 0xff, 0xfb, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xff, 0xff, 0x60, 0x0,
    0xaf, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0xc, 0xff, 0xfe, 0x52, 0x7f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0x60, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xff, 0xfb,
    0x30, 0x0, 0x0, 0x7d, 0xff, 0xfc, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x6, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x7, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x7, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x7, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x7,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x7, 0xff,
    0xff, 0xfc, 0x28, 0x99, 0x99, 0x30, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x3, 0x66, 0x66,
    0x66, 0xdf, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x32, 0x22, 0x22, 0x22, 0xff,
    0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x60,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x60,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0x90, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x46, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x64, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0xff, 0xff, 0xfd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xe2, 0x0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xfe,
    0x20, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xe1,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x56, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc5, 0x23, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0x1, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10,
    0x0,

    /* U+F0C9 "" */
    0x8c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x7b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xb5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x42,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x1, 0xc3, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x6f, 0xff, 0x80, 0x2, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0xa, 0xff, 0xff, 0xfc, 0x20, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x20, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x30, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4e, 0xff, 0xff,
    0xff, 0xe3, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x1, 0xbf,
    0xff, 0xfa, 0x10, 0x2d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x3, 0x99, 0x30, 0x5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x10, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x77, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x70,

    /* U+F0E7 "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x55, 0x55, 0x55, 0x51, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x22, 0x2a,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x39, 0xb9, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xaa,
    0xaa, 0xaf, 0xff, 0xff, 0xff, 0xaa, 0xaa, 0xa9,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xf6, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xbb, 0xbb,
    0xbb, 0xbb, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x12, 0x22, 0x22, 0x22,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x7, 0xe4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x7,
    0xff, 0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x7, 0xff,
    0xf4, 0x0, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x7, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x7, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x7, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x1, 0x44, 0x44, 0x43, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xed, 0xdd, 0xdd, 0xdc, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0x80, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x3a, 0xaa, 0xaa,
    0xaa, 0x50, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x98,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6c, 0xff, 0xff, 0xff, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x32, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x43, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xf6, 0x0, 0x6, 0xfe, 0x0,
    0x3, 0xff, 0x10, 0x1, 0xff, 0x50, 0x0, 0x5f,
    0xf1, 0x0, 0x1f, 0xff, 0xf4, 0xff, 0xff, 0x40,
    0x0, 0x4f, 0xd0, 0x0, 0x2f, 0xf0, 0x0, 0xe,
    0xf3, 0x0, 0x3, 0xfe, 0x0, 0x0, 0xff, 0xff,
    0x4f, 0xff, 0xf4, 0x0, 0x4, 0xfd, 0x0, 0x2,
    0xff, 0x0, 0x0, 0xef, 0x30, 0x0, 0x3f, 0xe0,
    0x0, 0xf, 0xff, 0xf4, 0xff, 0xff, 0x60, 0x0,
    0x7f, 0xe0, 0x0, 0x4f, 0xf2, 0x0, 0x1f, 0xf5,
    0x0, 0x5, 0xff, 0x10, 0x2, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xa2, 0x22, 0x6f, 0xf3, 0x22, 0x3f,
    0xf6, 0x22, 0x2e, 0xf9, 0x22, 0x27, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0xff, 0x30, 0x0, 0xcf, 0x50,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x2f, 0xe0, 0x0, 0xf, 0xf3,
    0x0, 0xc, 0xf5, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x3, 0xfe,
    0x0, 0x0, 0xff, 0x30, 0x0, 0xdf, 0x60, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xfd, 0xdd, 0xff, 0xfe, 0xdd, 0xef, 0xff, 0xdd,
    0xef, 0xff, 0xdd, 0xdf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xfa, 0x44, 0x4a,
    0xff, 0x54, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x9f, 0xf6, 0x44, 0x7f, 0xff, 0xf4, 0xff,
    0xff, 0x40, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0xff, 0xff, 0x4f, 0xff, 0xf4, 0x0, 0x4, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0xf, 0xff, 0xf4, 0xff, 0xff,
    0x40, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0xff,
    0xff, 0x4f, 0xff, 0xf9, 0x44, 0x4a, 0xff, 0x54,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x9f,
    0xf5, 0x44, 0x6f, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7b, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8f, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x1, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x56, 0x66, 0x66, 0x66, 0x66, 0x67,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x28, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x70,
    0x8, 0x20, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xf, 0xe2, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0xf, 0xfe, 0x20, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xf,
    0xff, 0xe2, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xf, 0xff, 0xfe, 0x20,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xf, 0xff, 0xff, 0xe2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xf, 0xff,
    0xff, 0xfe, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xff, 0xe2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x3, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x10,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x68, 0x9a,
    0xa9, 0x87, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x5a, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x10, 0x0,
    0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdc, 0xbb, 0xbc, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x7b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0xff,
    0xc1, 0x9, 0xff, 0xff, 0xff, 0xff, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xe2, 0xef,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0x64, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xb0, 0x4, 0xff, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x7a, 0xce, 0xff, 0xfd, 0xb9, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb0, 0x0,
    0x3, 0x91, 0x0, 0x0, 0x0, 0x1, 0x8e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x40, 0x0,
    0x0, 0x0, 0x5, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x76,
    0x56, 0x8b, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x45, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xf, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xf4, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xf5, 0xff, 0xff, 0x40, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xf4, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0x40, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xff, 0xf4, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0x40, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xef, 0xff, 0x5f,
    0xff, 0xf4, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0x8f, 0xff, 0xf5, 0xff, 0xff, 0x40,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xf4, 0x5, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x80, 0xf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f,
    0xff, 0xfc, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x0,
    0x0,

    /* U+F241 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xf, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xf4, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf5, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x3, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0x40, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xff, 0xf4, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0x40, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x5f,
    0xff, 0xf4, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x8, 0x8f, 0xff, 0xf5, 0xff, 0xff, 0x40,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xf4, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x30, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f,
    0xff, 0xfc, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x0,
    0x0,

    /* U+F242 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xf, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xf4, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf5, 0xff, 0xff, 0x40, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0x40, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xff, 0xf4, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0x40, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x5f,
    0xff, 0xf4, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x8f, 0xff, 0xf5, 0xff, 0xff, 0x40,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xf4, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f,
    0xff, 0xfc, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x0,
    0x0,

    /* U+F243 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xf, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xf4, 0x9, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf5, 0xff, 0xff, 0x40, 0x9f,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xf4, 0x9, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0x40, 0x9f, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xff, 0xf4, 0x9,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0x40, 0x9f, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x5f,
    0xff, 0xf4, 0x9, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x8f, 0xff, 0xf5, 0xff, 0xff, 0x40,
    0x9f, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xf4, 0x4, 0x88, 0x88,
    0x88, 0x87, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f,
    0xff, 0xfc, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x0,
    0x0,

    /* U+F244 "" */
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xf, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf5, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x3f, 0xff, 0xf5, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x5f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x8f, 0xff, 0xf5, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f,
    0xff, 0xfc, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xcc, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x57, 0x7a, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xef, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x2f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0x0, 0x0, 0x4e, 0xff, 0xc2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x10,
    0x0, 0x0, 0x3, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x86, 0x20,
    0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6c, 0x30, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0x90, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x91, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x1b, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xe6, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xcf, 0xff, 0xff,
    0xff, 0xf7, 0x55, 0x55, 0x55, 0x8f, 0xfe, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5b, 0xff,
    0xff, 0xc3, 0x5, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x50, 0x0,
    0x9, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x4, 0xbd,
    0xc8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf2, 0x0, 0xd, 0xff, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xc0, 0x0, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc9,
    0x9f, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xac, 0xcf, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0xab, 0xcd, 0xcb,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0x12, 0xef, 0xff, 0xff, 0xff, 0xe1,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x2e, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x3, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x3f, 0xff, 0xff, 0xff, 0x80,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xd0, 0xf, 0xff, 0xff,
    0xec, 0xff, 0xff, 0x10, 0x39, 0x0, 0x5f, 0xff,
    0xff, 0xf1, 0x4f, 0xff, 0xfe, 0x20, 0xbf, 0xff,
    0x10, 0x3f, 0xa0, 0x5, 0xff, 0xff, 0xf4, 0x7f,
    0xff, 0xfb, 0x0, 0xb, 0xff, 0x10, 0x3f, 0xf6,
    0x0, 0xaf, 0xff, 0xf7, 0x9f, 0xff, 0xff, 0xa0,
    0x0, 0xbf, 0x10, 0x3f, 0xa0, 0x5, 0xff, 0xff,
    0xf9, 0xbf, 0xff, 0xff, 0xfa, 0x0, 0xb, 0x10,
    0x2a, 0x0, 0x3f, 0xff, 0xff, 0xfa, 0xcf, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xfc, 0xdf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xfc,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfc, 0xdf,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0x60, 0x1, 0x0, 0x11, 0x0, 0xbf, 0xff, 0xff,
    0xfb, 0xaf, 0xff, 0xff, 0xf6, 0x0, 0x1d, 0x10,
    0x3d, 0x10, 0xb, 0xff, 0xff, 0xfa, 0x8f, 0xff,
    0xff, 0x60, 0x1, 0xdf, 0x10, 0x3f, 0xc1, 0x0,
    0xcf, 0xff, 0xf8, 0x6f, 0xff, 0xfa, 0x0, 0x2d,
    0xff, 0x10, 0x3f, 0xf4, 0x0, 0x7f, 0xff, 0xf6,
    0x3f, 0xff, 0xff, 0x52, 0xef, 0xff, 0x20, 0x3f,
    0x50, 0x6, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0x20, 0x35, 0x0, 0x6f, 0xff,
    0xff, 0xf0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x7, 0xff, 0xff, 0xff, 0xb0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0x60, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x7, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x7f, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0x38, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xbf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8b, 0xef, 0xff, 0xff, 0xeb, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x88, 0x88,
    0x88, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x36, 0x66,
    0x66, 0x66, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0x66, 0x66, 0x66, 0x61, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x9d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xfa, 0xa,
    0xff, 0xfe, 0x14, 0xff, 0xff, 0x61, 0xdf, 0xff,
    0xfa, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x7, 0xff,
    0xfd, 0x1, 0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd,
    0x1, 0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0,
    0x0, 0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1,
    0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0,
    0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff,
    0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef,
    0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff, 0xff,
    0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff,
    0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xf7,
    0x7, 0xff, 0xfd, 0x1, 0xff, 0xff, 0x30, 0xbf,
    0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x7,
    0xff, 0xfd, 0x1, 0xff, 0xff, 0x30, 0xbf, 0xff,
    0xfa, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x7, 0xff,
    0xfd, 0x1, 0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd,
    0x1, 0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0,
    0x0, 0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1,
    0xff, 0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0,
    0xef, 0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff,
    0xff, 0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef,
    0xff, 0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff, 0xff,
    0x30, 0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff,
    0xf7, 0x7, 0xff, 0xfd, 0x1, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xf7,
    0x7, 0xff, 0xfd, 0x1, 0xff, 0xff, 0x30, 0xbf,
    0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xfa, 0xa,
    0xff, 0xfe, 0x14, 0xff, 0xff, 0x61, 0xdf, 0xff,
    0xfa, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x13, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x30, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0x93, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x60,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0x60, 0xa, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x60, 0xa, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x60, 0xa, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xfd, 0xb9, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x4, 0xff, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x4,
    0xff, 0xf4, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x4, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x1, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x8, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xfb, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0xb, 0xff,
    0xfb, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0xb, 0xff, 0xff, 0xfb, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x5b, 0xff, 0xff, 0xff, 0xfb, 0x5e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc5, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xcf, 0xf9,
    0x44, 0x7f, 0xf4, 0x45, 0xff, 0x54, 0x4f, 0xff,
    0xf5, 0x0, 0xc, 0xff, 0xf7, 0x0, 0x3f, 0xe0,
    0x1, 0xff, 0x10, 0xe, 0xff, 0xf5, 0x0, 0xcf,
    0xff, 0xf7, 0x0, 0x3f, 0xe0, 0x1, 0xff, 0x10,
    0xe, 0xff, 0xf5, 0xc, 0xff, 0xff, 0xf7, 0x0,
    0x3f, 0xe0, 0x1, 0xff, 0x10, 0xe, 0xff, 0xf5,
    0xcf, 0xff, 0xff, 0xf7, 0x0, 0x3f, 0xe0, 0x1,
    0xff, 0x10, 0xe, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x3f, 0xe0, 0x1, 0xff, 0x10, 0xe,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xf8, 0x22, 0x4f,
    0xe2, 0x22, 0xff, 0x22, 0x2e, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x2, 0x34, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x43, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x3e, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x5f, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0x20, 0x0, 0x7f,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf2, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xd4, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x6f, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 146, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 146, .box_w = 5, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 60, .adv_w = 213, .box_w = 10, .box_h = 10, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 110, .adv_w = 382, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 386, .adv_w = 338, .box_w = 19, .box_h = 32, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 690, .adv_w = 459, .box_w = 27, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1014, .adv_w = 373, .box_w = 22, .box_h = 25, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1289, .adv_w = 114, .box_w = 3, .box_h = 10, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 1304, .adv_w = 183, .box_w = 8, .box_h = 32, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 1432, .adv_w = 184, .box_w = 8, .box_h = 32, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1560, .adv_w = 218, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 12},
    {.bitmap_index = 1645, .adv_w = 317, .box_w = 16, .box_h = 15, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 1765, .adv_w = 123, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1795, .adv_w = 208, .box_w = 11, .box_h = 3, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1812, .adv_w = 123, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1827, .adv_w = 191, .box_w = 15, .box_h = 32, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2067, .adv_w = 363, .box_w = 21, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2319, .adv_w = 201, .box_w = 10, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2439, .adv_w = 312, .box_w = 19, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2667, .adv_w = 311, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2883, .adv_w = 364, .box_w = 22, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3147, .adv_w = 312, .box_w = 19, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3375, .adv_w = 336, .box_w = 20, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3615, .adv_w = 325, .box_w = 18, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3831, .adv_w = 350, .box_w = 20, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4071, .adv_w = 336, .box_w = 20, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4311, .adv_w = 123, .box_w = 6, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4365, .adv_w = 123, .box_w = 6, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 4434, .adv_w = 317, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 4562, .adv_w = 317, .box_w = 16, .box_h = 11, .ofs_x = 2, .ofs_y = 6},
    {.bitmap_index = 4650, .adv_w = 317, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 4778, .adv_w = 312, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4994, .adv_w = 562, .box_w = 33, .box_h = 31, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 5506, .adv_w = 398, .box_w = 26, .box_h = 24, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5818, .adv_w = 412, .box_w = 22, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6082, .adv_w = 393, .box_w = 23, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6358, .adv_w = 449, .box_w = 24, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6646, .adv_w = 364, .box_w = 18, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6862, .adv_w = 345, .box_w = 18, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7078, .adv_w = 420, .box_w = 23, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7354, .adv_w = 442, .box_w = 22, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7618, .adv_w = 169, .box_w = 4, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7666, .adv_w = 279, .box_w = 16, .box_h = 24, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7858, .adv_w = 391, .box_w = 22, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8122, .adv_w = 323, .box_w = 17, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8326, .adv_w = 520, .box_w = 26, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8638, .adv_w = 442, .box_w = 22, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8902, .adv_w = 457, .box_w = 26, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9214, .adv_w = 393, .box_w = 20, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9454, .adv_w = 457, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9860, .adv_w = 395, .box_w = 21, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 10112, .adv_w = 338, .box_w = 19, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10340, .adv_w = 319, .box_w = 20, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10580, .adv_w = 430, .box_w = 21, .box_h = 24, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 10832, .adv_w = 387, .box_w = 26, .box_h = 24, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11144, .adv_w = 613, .box_w = 37, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11588, .adv_w = 366, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11864, .adv_w = 352, .box_w = 24, .box_h = 24, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12152, .adv_w = 357, .box_w = 21, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12404, .adv_w = 181, .box_w = 8, .box_h = 32, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 12532, .adv_w = 191, .box_w = 15, .box_h = 32, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 12772, .adv_w = 181, .box_w = 8, .box_h = 32, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12900, .adv_w = 317, .box_w = 16, .box_h = 14, .ofs_x = 2, .ofs_y = 5},
    {.bitmap_index = 13012, .adv_w = 272, .box_w = 17, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13038, .adv_w = 326, .box_w = 10, .box_h = 5, .ofs_x = 3, .ofs_y = 20},
    {.bitmap_index = 13063, .adv_w = 325, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13216, .adv_w = 371, .box_w = 19, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 13454, .adv_w = 311, .box_w = 18, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13616, .adv_w = 371, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13866, .adv_w = 333, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14037, .adv_w = 192, .box_w = 14, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14212, .adv_w = 375, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 14462, .adv_w = 370, .box_w = 18, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 14687, .adv_w = 152, .box_w = 5, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14752, .adv_w = 154, .box_w = 12, .box_h = 33, .ofs_x = -4, .ofs_y = -7},
    {.bitmap_index = 14950, .adv_w = 335, .box_w = 18, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15175, .adv_w = 152, .box_w = 4, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15225, .adv_w = 575, .box_w = 31, .box_h = 18, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15504, .adv_w = 370, .box_w = 18, .box_h = 18, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15666, .adv_w = 345, .box_w = 20, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15846, .adv_w = 371, .box_w = 19, .box_h = 25, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 16084, .adv_w = 371, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 16334, .adv_w = 223, .box_w = 10, .box_h = 18, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16424, .adv_w = 273, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16577, .adv_w = 225, .box_w = 14, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16731, .adv_w = 368, .box_w = 18, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 16893, .adv_w = 304, .box_w = 21, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17082, .adv_w = 489, .box_w = 31, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17361, .adv_w = 300, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17532, .adv_w = 304, .box_w = 21, .box_h = 25, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 17795, .adv_w = 283, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17939, .adv_w = 191, .box_w = 11, .box_h = 32, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 18115, .adv_w = 163, .box_w = 4, .box_h = 32, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 18179, .adv_w = 191, .box_w = 10, .box_h = 32, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 18339, .adv_w = 317, .box_w = 17, .box_h = 6, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 18390, .adv_w = 228, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 18456, .adv_w = 171, .box_w = 7, .box_h = 7, .ofs_x = 2, .ofs_y = 6},
    {.bitmap_index = 18481, .adv_w = 544, .box_w = 35, .box_h = 36, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 19111, .adv_w = 544, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19553, .adv_w = 544, .box_w = 34, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 20080, .adv_w = 544, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20522, .adv_w = 374, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20810, .adv_w = 544, .box_w = 34, .box_h = 34, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21388, .adv_w = 544, .box_w = 32, .box_h = 35, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 21948, .adv_w = 612, .box_w = 39, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22553, .adv_w = 544, .box_w = 34, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 23148, .adv_w = 612, .box_w = 39, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23655, .adv_w = 544, .box_w = 34, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 24250, .adv_w = 272, .box_w = 17, .box_h = 27, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24480, .adv_w = 408, .box_w = 26, .box_h = 27, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24831, .adv_w = 612, .box_w = 39, .box_h = 33, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 25475, .adv_w = 544, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25917, .adv_w = 374, .box_w = 24, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 26337, .adv_w = 476, .box_w = 22, .box_h = 32, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 26689, .adv_w = 476, .box_w = 30, .box_h = 36, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 27229, .adv_w = 476, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 27694, .adv_w = 476, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 28159, .adv_w = 476, .box_w = 22, .box_h = 32, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 28511, .adv_w = 476, .box_w = 32, .box_h = 31, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29007, .adv_w = 340, .box_w = 19, .box_h = 30, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 29292, .adv_w = 340, .box_w = 19, .box_h = 30, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 29577, .adv_w = 476, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 30042, .adv_w = 476, .box_w = 30, .box_h = 7, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 30147, .adv_w = 612, .box_w = 39, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30654, .adv_w = 680, .box_w = 43, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 31407, .adv_w = 612, .box_w = 40, .box_h = 35, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 32107, .adv_w = 544, .box_w = 34, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 32634, .adv_w = 476, .box_w = 30, .box_h = 19, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 32919, .adv_w = 476, .box_w = 30, .box_h = 19, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 33204, .adv_w = 680, .box_w = 44, .box_h = 27, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 33798, .adv_w = 544, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 34240, .adv_w = 544, .box_w = 34, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 34835, .adv_w = 544, .box_w = 35, .box_h = 36, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 35465, .adv_w = 476, .box_w = 31, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 35946, .adv_w = 476, .box_w = 30, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 36471, .adv_w = 476, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 36936, .adv_w = 476, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 37341, .adv_w = 544, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 37783, .adv_w = 340, .box_w = 23, .box_h = 35, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 38186, .adv_w = 476, .box_w = 30, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 38711, .adv_w = 476, .box_w = 30, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 39236, .adv_w = 612, .box_w = 39, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 39743, .adv_w = 544, .box_w = 36, .box_h = 36, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 40391, .adv_w = 408, .box_w = 26, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 40846, .adv_w = 680, .box_w = 43, .box_h = 32, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 41534, .adv_w = 680, .box_w = 43, .box_h = 22, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 42007, .adv_w = 680, .box_w = 43, .box_h = 22, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 42480, .adv_w = 680, .box_w = 43, .box_h = 22, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 42953, .adv_w = 680, .box_w = 43, .box_h = 22, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 43426, .adv_w = 680, .box_w = 43, .box_h = 22, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 43899, .adv_w = 680, .box_w = 43, .box_h = 27, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 44480, .adv_w = 476, .box_w = 26, .box_h = 35, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 44935, .adv_w = 476, .box_w = 30, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 45460, .adv_w = 544, .box_w = 35, .box_h = 35, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 46073, .adv_w = 680, .box_w = 43, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 46632, .adv_w = 408, .box_w = 26, .box_h = 35, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 47087, .adv_w = 547, .box_w = 35, .box_h = 22, .ofs_x = 0, .ofs_y = 2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 5, 0, 0, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 24, 0, 15, -12, 0, 0,
    0, 0, -30, -33, 4, 26, 12, 9,
    -22, 4, 27, 2, 23, 5, 17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 33, 4, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 0, -16, 0, 0, 0, 0,
    0, -11, 9, 11, 0, 0, -5, 0,
    -4, 5, 0, -5, 0, -5, -3, -11,
    0, 0, 0, 0, -5, 0, 0, -7,
    -8, 0, 0, -5, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    -5, 0, -8, 0, -15, 0, -66, 0,
    0, -11, 0, 11, 16, 1, 0, -11,
    5, 5, 18, 11, -9, 11, 0, 0,
    -31, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -20, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, -7, -27, 0, -22,
    -4, 0, 0, 0, 0, 1, 21, 0,
    -16, -4, -2, 2, 0, -9, 0, 0,
    -4, -40, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -44, -4, 21,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -22, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 18,
    0, 5, 0, 0, -11, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 21, 4,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -20, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    11, 5, 16, -5, 0, 0, 11, -5,
    -18, -75, 4, 15, 11, 1, -7, 0,
    20, 0, 17, 0, 17, 0, -51, 0,
    -7, 16, 0, 18, -5, 11, 5, 0,
    0, 2, -5, 0, 0, -9, 44, 0,
    44, 0, 16, 0, 23, 7, 9, 16,
    0, 0, 0, -20, 0, 0, 0, 0,
    2, -4, 0, 4, -10, -7, -11, 4,
    0, -5, 0, 0, 0, -22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -35, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, -30, 0, -34, 0, 0, 0,
    0, -4, 0, 54, -7, -7, 5, 5,
    -5, 0, -7, 5, 0, 0, -29, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -53, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -34, 0, 33, 0, 0, -20, 0,
    18, 0, -37, -53, -37, -11, 16, 0,
    0, -36, 0, 7, -13, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 14, 16, -66, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 26, 0, 4, 0, 0, 0,
    0, 0, 4, 4, -7, -11, 0, -2,
    -2, -5, 0, 0, -4, 0, 0, 0,
    -11, 0, -4, 0, -13, -11, 0, -14,
    -18, -18, -10, 0, -11, 0, -11, 0,
    0, 0, 0, -4, 0, 0, 5, 0,
    4, -5, 0, 2, 0, 0, 0, 5,
    -4, 0, 0, 0, -4, 5, 5, -2,
    0, 0, 0, -10, 0, -2, 0, 0,
    0, 0, 0, 2, 0, 7, -4, 0,
    -7, 0, -9, 0, 0, -4, 0, 16,
    0, 0, -5, 0, 0, 0, 0, 0,
    -2, 2, -4, -4, 0, 0, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -3, 0, -5, -7, 0,
    0, 0, 0, 0, 2, 0, 0, -4,
    0, -5, -5, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -4, -7, 0, -8, 0, -16,
    -4, -16, 11, 0, 0, -11, 5, 11,
    15, 0, -14, -2, -7, 0, -2, -26,
    5, -4, 4, -29, 5, 0, 0, 2,
    -28, 0, -29, -4, -47, -4, 0, -27,
    0, 11, 15, 0, 7, 0, 0, 0,
    0, 1, 0, -10, -7, 0, -16, 0,
    0, 0, -5, 0, 0, 0, -5, 0,
    0, 0, 0, 0, -3, -3, 0, -3,
    -7, 0, 0, 0, 0, 0, 0, 0,
    -5, -5, 0, -4, -7, -4, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -7,
    0, -4, 0, -11, 5, 0, 0, -7,
    3, 5, 5, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 4,
    0, 0, -5, 0, -5, -4, -7, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    -4, 0, 0, 0, 0, -6, -8, 0,
    -10, 0, 16, -4, 2, -17, 0, 0,
    15, -27, -28, -23, -11, 5, 0, -4,
    -35, -10, 0, -10, 0, -11, 8, -10,
    -35, 0, -15, 0, 0, 3, -2, 4,
    -4, 0, 5, 1, -16, -21, 0, -27,
    -13, -11, -13, -16, -7, -15, -1, -10,
    -15, 3, 0, 2, 0, -5, 0, 0,
    0, 4, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, -3, 0, -2, -5, 0, -9, -12,
    -12, -2, 0, -16, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 2,
    -3, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 26, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    -10, 0, 0, 0, 0, -27, -16, 0,
    0, 0, -8, -27, 0, 0, -5, 5,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, -10,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, -10, 0,
    0, 0, 0, 7, 0, 4, -11, -11,
    0, -5, -5, -7, 0, 0, 0, 0,
    0, 0, -16, 0, -5, 0, -8, -5,
    0, -12, -14, -16, -4, 0, -11, 0,
    -16, 0, 0, 0, 0, 44, 0, 0,
    3, 0, 0, -7, 0, 5, 0, -23,
    0, 0, 0, 0, 0, -51, -10, 18,
    16, -4, -23, 0, 5, -8, 0, -27,
    -3, -7, 5, -38, -5, 7, 0, 8,
    -19, -8, -20, -18, -23, 0, 0, -33,
    0, 31, 0, 0, -3, 0, 0, 0,
    -3, -3, -5, -15, -18, -1, -51, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -3, -5, -8, 0, 0,
    -11, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -11, 0, 0, 11,
    -2, 7, 0, -12, 5, -4, -2, -14,
    -5, 0, -7, -5, -4, 0, -8, -9,
    0, 0, -4, -2, -4, -9, -7, 0,
    0, -5, 0, 5, -4, 0, -12, 0,
    0, 0, -11, 0, -9, 0, -9, -9,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 5, 0, -8, 0, -4, -7,
    -17, -4, -4, -4, -2, -4, -7, -2,
    0, 0, 0, 0, 0, -5, -4, -4,
    0, 0, 0, 0, 7, -4, 0, -4,
    0, 0, 0, -4, -7, -4, -5, -7,
    -5, 0, 4, 22, -2, 0, -15, 0,
    -4, 11, 0, -5, -23, -7, 8, 1,
    0, -26, -9, 5, -9, 4, 0, -4,
    -4, -17, 0, -8, 3, 0, 0, -9,
    0, 0, 0, 5, 5, -11, -10, 0,
    -9, -5, -8, -5, -5, 0, -9, 3,
    -10, -9, 16, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, -7,
    0, 0, -5, -5, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -8, 0, -11, 0, 0, 0, -18, 0,
    4, -12, 11, 1, -4, -26, 0, 0,
    -12, -5, 0, -22, -14, -15, 0, 0,
    -23, -5, -22, -21, -26, 0, -14, 0,
    4, 36, -7, 0, -13, -5, -2, -5,
    -9, -15, -10, -20, -22, -13, -5, 0,
    0, -4, 0, 2, 0, 0, -38, -5,
    16, 12, -12, -20, 0, 2, -17, 0,
    -27, -4, -5, 11, -50, -7, 2, 0,
    0, -35, -7, -28, -5, -40, 0, 0,
    -38, 0, 32, 2, 0, -4, 0, 0,
    0, 0, -3, -4, -21, -4, 0, -35,
    0, 0, 0, 0, -17, 0, -5, 0,
    -2, -15, -26, 0, 0, -3, -8, -16,
    -5, 0, -4, 0, 0, 0, 0, -24,
    -5, -18, -17, -4, -9, -14, -5, -9,
    0, -11, -5, -18, -8, 0, -7, -10,
    -5, -10, 0, 3, 0, -4, -18, 0,
    11, 0, -10, 0, 0, 0, 0, 7,
    0, 4, -11, 22, 0, -5, -5, -7,
    0, 0, 0, 0, 0, 0, -16, 0,
    -5, 0, -8, -5, 0, -12, -14, -16,
    -4, 0, -11, 4, 22, 0, 0, 0,
    0, 44, 0, 0, 3, 0, 0, -7,
    0, 5, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -4, -11, 0, 0, 0, 0, 0, -3,
    0, 0, 0, -5, -5, 0, 0, -11,
    -5, 0, 0, -11, 0, 9, -3, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, 8, 11, 4, -5, 0, -17,
    -9, 0, 16, -18, -17, -11, -11, 22,
    10, 5, -47, -4, 11, -5, 0, -5,
    6, -5, -19, 0, -5, 5, -7, -4,
    -16, -4, 0, 0, 16, 11, 0, -15,
    0, -30, -7, 16, -7, -21, 2, -7,
    -18, -18, -5, 22, 5, 0, -8, 0,
    -15, 0, 4, 18, -13, -20, -22, -14,
    16, 0, 2, -40, -4, 5, -9, -4,
    -13, 0, -12, -20, -8, -8, -4, 0,
    0, -13, -11, -5, 0, 16, 13, -5,
    -30, 0, -30, -8, 0, -19, -32, -2,
    -17, -9, -18, -15, 15, 0, 0, -7,
    0, -11, -5, 0, -5, -10, 0, 9,
    -18, 5, 0, 0, -29, 0, -5, -12,
    -9, -4, -16, -14, -18, -13, 0, -16,
    -5, -13, -10, -16, -5, 0, 0, 2,
    26, -9, 0, -16, -5, 0, -5, -11,
    -13, -15, -15, -21, -7, -11, 11, 0,
    -8, 0, -27, -7, 3, 11, -17, -20,
    -11, -18, 18, -5, 3, -51, -10, 11,
    -12, -9, -20, 0, -16, -23, -7, -5,
    -4, -5, -11, -16, -2, 0, 0, 16,
    15, -4, -35, 0, -33, -13, 13, -21,
    -37, -11, -19, -23, -27, -18, 11, 0,
    0, 0, 0, -7, 0, 0, 5, -7,
    11, 4, -10, 11, 0, 0, -17, -2,
    0, -2, 0, 2, 2, -4, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 4, 16, 1, 0, -7, 0, 0,
    0, 0, -4, -4, -7, 0, 0, 0,
    2, 4, 0, 0, 0, 0, 4, 0,
    -4, 0, 21, 0, 10, 2, 2, -7,
    0, 11, 0, 0, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 16, 0, 15, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -33, 0, -5, 9, 0, 16,
    0, 0, 54, 7, -11, -11, 5, 5,
    -4, 2, -27, 0, 0, 26, -33, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -37, 21, 76, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -33, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, -10,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, -15, 0,
    0, 2, 0, 0, 5, 70, -11, -4,
    17, 15, -15, 5, 0, 0, 5, 5,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -71, 15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -15,
    0, 0, 0, -15, 0, 0, 0, 0,
    -12, -3, 0, 0, 0, -12, 0, -7,
    0, -26, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -36, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, -5, 0, 0, -10, 0, -8, 0,
    -15, 0, 0, 0, -9, 5, -7, 0,
    0, -15, -5, -13, 0, 0, -15, 0,
    -5, 0, -26, 0, -6, 0, 0, -44,
    -10, -22, -6, -20, 0, 0, -36, 0,
    -15, -3, 0, 0, 0, 0, 0, 0,
    0, 0, -8, -10, -4, -9, 0, 0,
    0, 0, -12, 0, -12, 7, -6, 11,
    0, -4, -13, -4, -9, -10, 0, -7,
    -3, -4, 4, -15, -2, 0, 0, 0,
    -48, -4, -8, 0, -12, 0, -4, -26,
    -5, 0, 0, -4, -4, 0, 0, 0,
    0, 4, 0, -4, -9, -4, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 0, 0, 0, 0,
    0, -12, 0, -4, 0, 0, 0, -11,
    5, 0, 0, 0, -15, -5, -11, 0,
    0, -15, 0, -5, 0, -26, 0, 0,
    0, 0, -53, 0, -11, -20, -27, 0,
    0, -36, 0, -4, -8, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -8, -3,
    -8, 2, 0, 0, 9, -7, 0, 17,
    27, -5, -5, -16, 7, 27, 9, 12,
    -15, 7, 23, 7, 16, 12, 15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 34, 26, -10, -5, 0, -4,
    44, 23, 44, 0, 0, 0, 5, 0,
    0, 20, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 8,
    0, 0, 0, 0, -46, -7, -4, -22,
    -27, 0, 0, -36, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    8, 0, 0, 0, 0, -46, -7, -4,
    -22, -27, 0, 0, -22, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, -13, 5, 0, -5,
    4, 10, 5, -16, 0, -1, -4, 5,
    0, 4, 0, 0, 0, 0, -14, 0,
    -5, -4, -11, 0, -5, -22, 0, 34,
    -5, 0, -12, -4, 0, -4, -9, 0,
    -5, -15, -11, -7, 0, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 0, 0, 0, -46,
    -7, -4, -22, -27, 0, 0, -36, 0,
    0, 0, 0, 0, 0, 27, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, -17, -7, -5, 16, -5, -5,
    -22, 2, -3, 2, -4, -15, 1, 12,
    1, 4, 2, 4, -13, -22, -7, 0,
    -21, -10, -15, -23, -21, 0, -9, -11,
    -7, -7, -4, -4, -7, -4, 0, -4,
    -2, 8, 0, 8, -4, 0, 17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, -5, -5, 0, 0,
    -15, 0, -3, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -33, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, 0, -7,
    0, 0, 0, 0, -4, 0, 0, -9,
    -5, 5, 0, -9, -10, -4, 0, -16,
    -4, -12, -4, -7, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -36, 0, 17, 0, 0, -10, 0,
    0, 0, 0, -7, 0, -5, 0, 0,
    -3, 0, 0, -4, 0, -13, 0, 0,
    23, -7, -18, -17, 4, 6, 6, -1,
    -15, 4, 8, 4, 16, 4, 18, -4,
    -15, 0, 0, -22, 0, 0, -16, -15,
    0, 0, -11, 0, -7, -9, 0, -8,
    0, -8, 0, -4, 8, 0, -4, -16,
    -5, 20, 0, 0, -5, 0, -11, 0,
    0, 7, -13, 0, 5, -5, 4, 1,
    0, -18, 0, -4, -2, 0, -5, 6,
    -4, 0, 0, 0, -22, -7, -12, 0,
    -16, 0, 0, -26, 0, 20, -5, 0,
    -10, 0, 3, 0, -5, 0, -5, -16,
    0, -5, 5, 0, 0, 0, 0, -4,
    0, 0, 5, -7, 2, 0, 0, -7,
    -4, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -34, 0, 12, 0,
    0, -4, 0, 0, 0, 0, 1, 0,
    -5, -5, 0, 0, 0, 11, 0, 13,
    0, 0, 0, 0, 0, -34, -31, 2,
    23, 16, 9, -22, 4, 23, 0, 20,
    0, 11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 29, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR >= 8
/*Store all the custom data of the font*/

static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,

};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_montserrat_34 = {
#else
lv_font_t lv_font_montserrat_34 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 38,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_34*/
