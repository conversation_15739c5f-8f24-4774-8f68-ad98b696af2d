/**
 * @file led_button_control.cpp
 * @brief ESP32-C3 LED和按键控制系统实现
 */

#include "led_button_control.h"
#include "tuya_log.h"
#include "shipping.h"
#include <esp_system.h>

static const char *TAG = "LED_BUTTON";

// FastLED对象
CRGB leds[NUM_LEDS];

// OneButton对象
OneButton button6(BUTTON_GPIO6, true, true);  // GPIO6, active LOW, enable pullup
OneButton button7(BUTTON_GPIO7, true, true);  // GPIO7, active LOW, enable pullup

// 全局状态变量
static device_state_t current_device_state = DEVICE_STATE_UNCONFIGURED;
static gpio_state_t current_gpio_state = {false, false};
static led_color_t current_led_color = LED_COLOR_RED;
static bool breathing_enabled = true;
static bool factory_reset_requested = false;

// 呼吸灯变量
static uint8_t breathing_brightness = 0;
static int8_t breathing_direction = 1;
static unsigned long last_breathing_update = 0;

// 按键长按计时
static unsigned long button6_press_start = 0;
static unsigned long button7_press_start = 0;
static bool button6_long_press_active = false;
static bool button7_long_press_active = false;

// 前向声明
void update_led_display(void);
void update_breathing_effect(void);
CRGB get_color_rgb(led_color_t color);

// 按键6事件处理
void button6_click() {
    TY_LOGI("Button GPIO6 clicked");
    toggle_gpio5_state();
}

void button6_press() {
    TY_LOGI("Button GPIO6 pressed");
    button6_press_start = millis();
    button6_long_press_active = true;
}

void button6_release() {
    TY_LOGI("Button GPIO6 released");
    button6_long_press_active = false;
}

// 按键7事件处理
void button7_click() {
    TY_LOGI("Button GPIO7 clicked");
    toggle_gpio5_gpio1_state();
}

void button7_press() {
    TY_LOGI("Button GPIO7 pressed");
    button7_press_start = millis();
    button7_long_press_active = true;
}

void button7_release() {
    TY_LOGI("Button GPIO7 released");
    button7_long_press_active = false;
}

void led_button_init(void) {
    TY_LOGI("Initializing LED and button control system");
    
    // 初始化FastLED
    FastLED.addLeds<LED_TYPE, LED_PIN, COLOR_ORDER>(leds, NUM_LEDS);
    FastLED.setBrightness(255);
    FastLED.clear();
    FastLED.show();
    
    // 初始化GPIO输出引脚
    pinMode(OUTPUT_GPIO5, OUTPUT);
    pinMode(OUTPUT_GPIO1, OUTPUT);
    digitalWrite(OUTPUT_GPIO5, LOW);
    digitalWrite(OUTPUT_GPIO1, LOW);
    
    // 配置按键6
    button6.attachClick(button6_click);
    button6.attachPress(button6_press);
    button6.attachRelease(button6_release);
    button6.setClickMs(50);
    button6.setDebounceMs(50);
    
    // 配置按键7
    button7.attachClick(button7_click);
    button7.attachPress(button7_press);
    button7.attachRelease(button7_release);
    button7.setClickMs(50);
    button7.setDebounceMs(50);
    
    // 初始化LED状态（未配网显示红色呼吸灯）
    set_led_color(LED_COLOR_RED, true);
    
    TY_LOGI("LED and button control system initialized");
}

void led_button_task(void) {
    // 更新按键状态
    button6.tick();
    button7.tick();
    
    // 检查工厂重置长按
    if (button6_long_press_active && (millis() - button6_press_start >= FACTORY_RESET_TIME)) {
        TY_LOGI("Button GPIO6 long press detected - factory reset requested");
        factory_reset_requested = true;
        button6_long_press_active = false;
    }
    
    if (button7_long_press_active && (millis() - button7_press_start >= FACTORY_RESET_TIME)) {
        TY_LOGI("Button GPIO7 long press detected - factory reset requested");
        factory_reset_requested = true;
        button7_long_press_active = false;
    }
    
    // 更新呼吸灯效果
    if (breathing_enabled) {
        update_breathing_effect();
    }
    
    // 更新LED显示
    update_led_display();
}

void set_led_color(led_color_t color, bool breathing) {
    current_led_color = color;
    breathing_enabled = breathing;
    
    if (!breathing) {
        breathing_brightness = 255;  // 固定亮度
    }
    
    TY_LOGI("LED color set to %d, breathing: %s", color, breathing ? "enabled" : "disabled");
}

void set_device_state(device_state_t state) {
    current_device_state = state;
    
    // 根据设备状态自动设置LED颜色
    switch (state) {
        case DEVICE_STATE_UNCONFIGURED:
            set_led_color(LED_COLOR_RED, true);  // 红色呼吸灯
            break;
        case DEVICE_STATE_CONFIGURED:
            set_led_color(LED_COLOR_WHITE, true);  // 白色呼吸灯
            break;
        case DEVICE_STATE_GPIO5_ACTIVE:
            set_led_color(LED_COLOR_GREEN, false);  // 绿色常亮
            break;
        case DEVICE_STATE_GPIO5_GPIO1_ACTIVE:
            set_led_color(LED_COLOR_BLUE, false);  // 蓝色常亮
            break;
    }
    
    TY_LOGI("Device state set to %d", state);
}

device_state_t get_device_state(void) {
    return current_device_state;
}

gpio_state_t get_gpio_state(void) {
    return current_gpio_state;
}

void set_gpio5_state(bool active) {
    current_gpio_state.gpio5_active = active;
    digitalWrite(OUTPUT_GPIO5, active ? HIGH : LOW);
    TY_LOGI("GPIO5 set to %s", active ? "HIGH" : "LOW");
}

void set_gpio1_state(bool active) {
    current_gpio_state.gpio1_active = active;
    digitalWrite(OUTPUT_GPIO1, active ? HIGH : LOW);
    TY_LOGI("GPIO1 set to %s", active ? "HIGH" : "LOW");
}

void toggle_gpio5_state(void) {
    bool new_state = !current_gpio_state.gpio5_active;
    set_gpio5_state(new_state);
    
    if (new_state) {
        // GPIO5激活，显示绿色
        set_device_state(DEVICE_STATE_GPIO5_ACTIVE);
    } else {
        // GPIO5关闭，恢复到配网状态的LED显示
        if (current_device_state == DEVICE_STATE_GPIO5_ACTIVE) {
            set_device_state(DEVICE_STATE_CONFIGURED);
        }
    }
}

void toggle_gpio5_gpio1_state(void) {
    bool new_state = !(current_gpio_state.gpio5_active && current_gpio_state.gpio1_active);
    
    set_gpio5_state(new_state);
    set_gpio1_state(new_state);
    
    if (new_state) {
        // GPIO5和GPIO1都激活，显示蓝色
        set_device_state(DEVICE_STATE_GPIO5_GPIO1_ACTIVE);
    } else {
        // GPIO5和GPIO1都关闭，恢复到配网状态的LED显示
        if (current_device_state == DEVICE_STATE_GPIO5_GPIO1_ACTIVE) {
            set_device_state(DEVICE_STATE_CONFIGURED);
        }
    }
}

void perform_factory_reset(void) {
    TY_LOGI("Performing factory reset...");
    
    // 显示特殊的工厂重置指示（快速闪烁红色）
    for (int i = 0; i < 10; i++) {
        leds[0] = CRGB::Red;
        FastLED.show();
        delay(100);
        leds[0] = CRGB::Black;
        FastLED.show();
        delay(100);
    }
    
    // 调用工厂重置函数
    factory_reset();
    
    // 重启系统
    TY_LOGI("Factory reset completed, restarting...");
    ESP.restart();
}

bool is_factory_reset_requested(void) {
    return factory_reset_requested;
}

void reset_factory_reset_request(void) {
    factory_reset_requested = false;
}

// 内部函数实现
void update_breathing_effect(void) {
    if (millis() - last_breathing_update >= BREATHING_SPEED) {
        breathing_brightness += breathing_direction * 5;
        
        if (breathing_brightness >= 255) {
            breathing_brightness = 255;
            breathing_direction = -1;
        } else if (breathing_brightness <= 30) {
            breathing_brightness = 30;
            breathing_direction = 1;
        }
        
        last_breathing_update = millis();
    }
}

void update_led_display(void) {
    CRGB color = get_color_rgb(current_led_color);
    
    if (breathing_enabled) {
        // 应用呼吸效果
        color.fadeToBlackBy(255 - breathing_brightness);
    }
    
    leds[0] = color;
    FastLED.show();
}

CRGB get_color_rgb(led_color_t color) {
    switch (color) {
        case LED_COLOR_WHITE:
            return CRGB::White;
        case LED_COLOR_RED:
            return CRGB::Red;
        case LED_COLOR_GREEN:
            return CRGB::Green;
        case LED_COLOR_BLUE:
            return CRGB::Blue;
        case LED_COLOR_OFF:
        default:
            return CRGB::Black;
    }
}
