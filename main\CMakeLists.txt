##
# @file CMakeLists.txt
# @brief Main application component CMakeLists for ESP-IDF
#/

# Get all source files
file(GLOB_RECURSE app_sources 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

# Include directories
set(app_includes
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/include
)

# Register component
idf_component_register(
    SRCS ${app_sources}
    INCLUDE_DIRS ${app_includes}
    REQUIRES 
        freertos
        esp_netif
        esp_event
        nvs_flash
        esp_wifi
        lwip
        mbedtls
        bt
        common
        tal_system
        tal_driver
        tal_kv
        tal_network
        tal_wifi
        tal_bluetooth
        tuya_cloud_service
        coreHTTP
        coreMQTT
        utils
)
