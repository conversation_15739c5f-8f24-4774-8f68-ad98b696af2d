#include "improv.h"

namespace improv
{
    void ImprovComponent::loadSavedNetworks() {
        saved_networks_.clear();
        
        Preferences preferences;
        preferences.begin("wifi_creds", true);  // true = 只读模式
        
        size_t networks_count = preferences.getUInt("count", 0);
        Serial.printf("Loading %d saved networks\n", networks_count);
        
        for (size_t i = 0; i < networks_count; i++) {
            String prefix = "net" + String(i);
            String ssid = preferences.getString((prefix + "_ssid").c_str(), "");
            String password = preferences.getString((prefix + "_pass").c_str(), "");
            
            if (ssid.length() > 0) {
                saved_networks_.push_back(WiFiCredential{
                    std::string(ssid.c_str()),
                    std::string(password.c_str())
                });
                Serial.printf("Loaded credentials for SSID: %s\n", ssid.c_str());
            }
        }
        
        preferences.end();
    }

    void ImprovComponent::saveWiFiCredential(const std::string& ssid, const std::string& password) {
        Serial.println("Saving WiFi credentials...");
        
        WiFiCredential new_cred{ssid, password};
        
        // 检查是否已存在相同SSID
        auto it = std::find(saved_networks_.begin(), saved_networks_.end(), new_cred);
        if (it != saved_networks_.end()) {
            Serial.println("Updating existing credentials");
            it->password = password;
        } else {
            Serial.println("Adding new credentials");
            if (saved_networks_.size() >= MAX_SAVED_NETWORKS) {
                Serial.println("Maximum networks reached, removing oldest");
                saved_networks_.erase(saved_networks_.begin());
            }
            saved_networks_.push_back(new_cred);
        }
    
        // 保存到flash存储
        Preferences preferences;
        if (!preferences.begin("wifi_creds", false)) {
            Serial.println("Failed to begin preferences");
            return;
        }
        
        // 保存网络数量
        size_t networks_count = saved_networks_.size();
        preferences.putUInt("count", networks_count);
        Serial.printf("Saving %d networks\n", networks_count);
        
        // 保存每个网络的凭据
        for (size_t i = 0; i < networks_count; i++) {
            String prefix = "net" + String(i);
            preferences.putString((prefix + "_ssid").c_str(), saved_networks_[i].ssid.c_str());
            preferences.putString((prefix + "_pass").c_str(), saved_networks_[i].password.c_str());
            Serial.printf("Saved network %d: %s\n", i, saved_networks_[i].ssid.c_str());
        }
        
        preferences.end();
        Serial.println("WiFi credentials saved successfully");
    }
    
    bool ImprovComponent::connectToSavedNetworks() {
        if (saved_networks_.empty()) {
            Serial.println("No saved networks found");
            return false;
        }
#if 0
        WiFi.disconnect(true, false);
        vTaskDelay(pdMS_TO_TICKS(300));
#endif
        WiFi.mode(WIFI_OFF);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.mode(WIFI_STA);
        vTaskDelay(pdMS_TO_TICKS(300));

        // 尝试连接每个保存的网络
        for (const auto& cred : saved_networks_) {
            Serial.printf("Attempting to connect to %s\n", cred.ssid.c_str());
            WiFi.begin(cred.ssid.c_str(), cred.password.c_str());
            
            // 等待连接
            int attempts = 0;
            const int MAX_ATTEMPTS = 3;
            while (attempts < MAX_ATTEMPTS) {
                if (WiFi.status() == WL_CONNECTED) {
                    Serial.printf("Successfully connected to %s\n", cred.ssid.c_str());
                    return true;
                }
                vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1000);
                attempts++;
            }

            vTaskDelay(pdMS_TO_TICKS(1000));  // 等待500);
        }

        return false;
    }
    
    void ImprovComponent::wifiMonitorTask(void* parameter) {
        const TickType_t CHECK_INTERVAL = pdMS_TO_TICKS(5000);  // 5秒检查一次

        Serial.println("wifiMonitorTask started\n");
        while (true) {
            if (WiFi.status() != WL_CONNECTED) {
                Serial.println("WiFi disconnected, attempting to reconnect...");
                // 尝试重新连接到保存的网络
                if (instance_->connectToSavedNetworks()) {
                    // 连接成功，继续循环
                    continue;
                }
            }

            vTaskDelay(CHECK_INTERVAL);
        }
    }

    // 静态成员初始化
    ImprovComponent* ImprovComponent::instance_ = nullptr;

    void ImprovComponent::setup()
    {
        Serial.println("Starting ImprovComponent setup...");
        instance_ = this;  // 设置单例指针
        // 首先初始化WiFi

        // 首先加载保存的网络
        loadSavedNetworks();
        Serial.printf("Loaded %d saved networks\n", saved_networks_.size());
#if 0
        // 尝试连接保存的网络
        if (!saved_networks_.empty()) {
            Serial.println("Attempting to connect to saved networks...");
            if (connectToSavedNetworks()) {
                Serial.println("Successfully connected to saved network");
            } else {
                Serial.println("Failed to connect to any saved network");
            }
        } else {
            Serial.println("No saved networks found during setup");
        }
#endif
        // 创建WiFi监控任务
        xTaskCreate(
            wifiMonitorTask,
            "WiFiMonitor",
            WIFI_MONITOR_STACK_SIZE,
            nullptr,
            WIFI_MONITOR_PRIORITY,
            &wifi_monitor_task_handle_
        );
    }

    bool ImprovComponent::wifi_connect(const std::string &ssid, const std::string &password) {
        Serial.printf("Attempting to connect to WiFi - SSID: %s\n", ssid.c_str());
#if 0
        WiFi.disconnect(true, false);
        vTaskDelay(pdMS_TO_TICKS(300));
#endif
        WiFi.mode(WIFI_OFF);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.mode(WIFI_STA);
        vTaskDelay(pdMS_TO_TICKS(300));

        WiFi.begin(ssid.c_str(), password.c_str());
        Serial.println("Connecting to WiFi...");
    
        int attempts = 0;
        const int max_attempts = 3;
        const unsigned long timeout_per_attempt = 5000;
    
        while (attempts < max_attempts) {
            attempts++;
            Serial.printf("Connection attempt %d of %d\n", attempts, max_attempts);
    
            unsigned long startTime = millis();
            while (WiFi.status() != WL_CONNECTED) {
                if (millis() - startTime > timeout_per_attempt) {
                    Serial.printf("Attempt %d timed out\n", attempts);
                    break;
                }
                vTaskDelay(pdMS_TO_TICKS(500));
                Serial.printf("WiFi status: %d\n", WiFi.status());
            }
    
            if (WiFi.status() == WL_CONNECTED) {
                Serial.println("\nConnected to WiFi!");
                Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
                // 保存凭据
                saveWiFiCredential(ssid, password);
                return true;
            }
    
            if (attempts < max_attempts) {
                Serial.println("\nRetrying connection...");
                vTaskDelay(pdMS_TO_TICKS(1000));
                WiFi.begin(ssid.c_str(), password.c_str());
            }
        }
    
        Serial.println("\nWiFi connection failed after all attempts");
        return false;
    }
} // namespace improv
