# This file was automatically generated for projects
# without default 'CMakeLists.txt' file.

# Get all source files in the src directory
file(GLOB_RECURSE app_sources
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

# Set include directories for all header files
set(app_includes
    "."
    "${CMAKE_SOURCE_DIR}/include"
    "${CMAKE_SOURCE_DIR}/lib/common/include"
    "${CMAKE_SOURCE_DIR}/lib/common/source/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_system/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_driver/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_kv/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_network/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_wifi/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_bluetooth/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_bluetooth/nimble/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_cli/include"
    "${CMAKE_SOURCE_DIR}/lib/tal_security/include"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/authorize"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/ble"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/cloud"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/lan"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/netcfg"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/netmgr"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/protocol"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/schema"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/tls"
    "${CMAKE_SOURCE_DIR}/lib/tuya_cloud_service/transport"
    "${CMAKE_SOURCE_DIR}/lib/coreHTTP/include"
    "${CMAKE_SOURCE_DIR}/lib/coreMQTT/include"
    "${CMAKE_SOURCE_DIR}/lib/utils/src"
    "${CMAKE_SOURCE_DIR}/lib/tuyaos_adapter/include"
    "${CMAKE_SOURCE_DIR}/lib/libqrencode"
    "${CMAKE_SOURCE_DIR}/lib/libhttp/include"
    "${CMAKE_SOURCE_DIR}/lib/libmqtt/include"
)

idf_component_register(
    SRCS ${app_sources}
    INCLUDE_DIRS ${app_includes}
    REQUIRES
        freertos
        esp_netif
        esp_event
        nvs_flash
        esp_wifi
        lwip
        mbedtls
        bt
        json
        common
        tal_system
        tal_driver
        tal_kv
        tal_network
        tal_wifi
        tal_bluetooth
        tuya_cloud_service
        coreHTTP
        coreMQTT
        utils
)
