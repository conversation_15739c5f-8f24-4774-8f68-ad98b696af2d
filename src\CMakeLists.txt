# 添加所有组件搜索路径
set(EXTRA_COMPONENT_DIRS 
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/common
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/coreHTTP
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/coreMQTT
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/libhttp
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/liblvgl
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/liblwip
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/libmqtt
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/libqrencode
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/peripherals
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_bluetooth
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_cli
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_driver
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_kv
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_network
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_security
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_system
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_wifi
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tal_wired
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tuya_ai_basic
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tuya_cloud_service
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tuyaos_adapter
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tuyaos_adapter/include/utilities/include
    ${CMAKE_CURRENT_SOURCE_DIR}/lib/tuya_cloud_service/netmgr
#    $ENV{IDF_PATH}/components
)

# This file was automatically generated for projects
# without default 'CMakeLists.txt' file.

# Get all source files in the src directory
file(GLOB_RECURSE app_sources
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

idf_component_register(
    SRCS ${app_sources}
    INCLUDE_DIRS "."
)
