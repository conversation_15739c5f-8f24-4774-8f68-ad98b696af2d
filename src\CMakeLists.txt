# This file was automatically generated for projects
# without default 'CMakeLists.txt' file.

FILE(GLOB_RECURSE app_sources ${CMAKE_SOURCE_DIR}/src/*.*)

idf_component_register(
    SRCS "main.c" 
        INCLUDE_DIRS "."
    REQUIRES
        freertos
        esp_netif
        esp_event
        nvs_flash
        esp_wifi
        lwip
        mbedtls
        bt
        common
        tal_system
        tal_driver
        tal_kv
        tal_network
        tal_wifi
        tal_bluetooth
        tuya_cloud_service
        coreHTTP
        coreMQTT
        utils
)
idf_component_register(SRCS "main.c"
                    INCLUDE_DIRS ".")