/**
 * @file tuya_debug.c
 * @brief Tuya IoT debugging utilities
 */

#include "tuya_debug.h"
#include "tuya_iot.h"
#include "tuya_config.h"
#include "storage_interface.h"
#include "tuya_log.h"
#include <string.h>

static const char *TAG = "TUYA_DEBUG";

void tuya_debug_print_config(const tuya_iot_config_t *config)
{
    TY_LOGI("=== Tuya IoT Configuration Debug ===");
    TY_LOGI("Product Key: %s", config->productkey ? config->productkey : "NULL");
    TY_LOGI("Device UUID: %s", config->uuid ? config->uuid : "NULL");
    TY_LOGI("Auth Key: %s", config->authkey ? config->authkey : "NULL");
    TY_LOGI("Software Ver: %s", config->software_ver ? config->software_ver : "NULL");
    TY_LOGI("Storage Namespace: %s", config->storage_namespace ? config->storage_namespace : "NULL");
    
    // 分析存储键名生成逻辑
    if (config->storage_namespace) {
        size_t namespace_len = strlen(config->storage_namespace);
        TY_LOGI("Storage namespace length: %zu", namespace_len);
        
        if (namespace_len > 15) {
            TY_LOGW("Namespace too long (%zu > 15), will use 'tuya_act' as storage key", namespace_len);
        } else {
            TY_LOGI("Will use '%s' as storage key", config->storage_namespace);
        }
    }
    TY_LOGI("=====================================");
}

void tuya_debug_analyze_storage_keys(const tuya_iot_config_t *config)
{
    TY_LOGI("=== Storage Key Analysis ===");
    
    if (!config || !config->storage_namespace) {
        TY_LOGE("Invalid config or missing storage_namespace");
        return;
    }
    
    const char *storage_namespace = config->storage_namespace;
    size_t namespace_len = strlen(storage_namespace);
    
    // 主激活数据键
    char activate_key[16] = {0};
    if (namespace_len > 15) {
        snprintf(activate_key, sizeof(activate_key), "tuya_act");
        TY_LOGI("Activate data key: %s (shortened from '%s')", activate_key, storage_namespace);
    } else {
        strncpy(activate_key, storage_namespace, sizeof(activate_key) - 1);
        TY_LOGI("Activate data key: %s", activate_key);
    }
    
    // 设备ID键
    char devid_key[32];
    if (namespace_len > 10) {
        snprintf(devid_key, sizeof(devid_key), "tuya_act.devid");
        TY_LOGI("Device ID key: %s (shortened)", devid_key);
    } else {
        snprintf(devid_key, sizeof(devid_key), "%s.devid", storage_namespace);
        TY_LOGI("Device ID key: %s", devid_key);
    }
    
    // 版本键
    char version_key[32];
    if (namespace_len > 11) {
        snprintf(version_key, sizeof(version_key), "tuya_act.ver");
        TY_LOGI("Version key: %s (shortened)", version_key);
    } else {
        snprintf(version_key, sizeof(version_key), "%s.ver", storage_namespace);
        TY_LOGI("Version key: %s", version_key);
    }
    
    TY_LOGI("============================");
}

void tuya_debug_check_activation_data(const tuya_iot_config_t *config)
{
    TY_LOGI("=== Checking Activation Data ===");
    
    if (!config || !config->storage_namespace) {
        TY_LOGE("Invalid config");
        return;
    }
    
    // 确定实际使用的存储键
    const char *storage_namespace = config->storage_namespace;
    char actual_storage_key[16] = {0};
    
    if (strlen(storage_namespace) > 15) {
        snprintf(actual_storage_key, sizeof(actual_storage_key), "tuya_act");
        TY_LOGI("Using shortened storage key: %s", actual_storage_key);
    } else {
        strncpy(actual_storage_key, storage_namespace, sizeof(actual_storage_key) - 1);
        TY_LOGI("Using full storage key: %s", actual_storage_key);
    }
    
    // 尝试读取激活数据
    size_t data_len = 1024;
    char *data_buffer = malloc(data_len);
    if (data_buffer) {
        int ret = local_storage_get(actual_storage_key, (uint8_t*)data_buffer, &data_len);
        if (ret == OPRT_OK) {
            TY_LOGI("Activation data found (%zu bytes)", data_len);
            TY_LOGI("Data preview: %.100s%s", data_buffer, data_len > 100 ? "..." : "");
        } else {
            TY_LOGI("No activation data found (error: %d)", ret);
        }
    }
    free(data_buffer);
    
    TY_LOGI("================================");
}

void tuya_debug_full_analysis(const tuya_iot_config_t *config)
{
    TY_LOGI("\n");
    TY_LOGI("######################################");
    TY_LOGI("#     TUYA IOT FULL DEBUG ANALYSIS   #");
    TY_LOGI("######################################");

    tuya_debug_print_config(config);
    tuya_debug_analyze_storage_keys(config);
    tuya_debug_check_activation_data(config);
    
    TY_LOGI("######################################");
    TY_LOGI("#         DEBUG ANALYSIS COMPLETE    #");
    TY_LOGI("######################################");
    TY_LOGI("\n");
}
