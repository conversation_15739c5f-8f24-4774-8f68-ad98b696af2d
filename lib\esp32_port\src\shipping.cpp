#include <Preferences.h>
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include "tuya_config.h"
#include "shipping.h"

Preferences prefs;

void set_shipping_mode(bool flag)
{
    prefs.begin("runMode", false);
    prefs.putBool("shipping", flag);
    prefs.end();
}

bool should_enter_shipping_mode()
{
    prefs.begin("runMode", true);
    bool flag = prefs.getBool("shipping", false);
    prefs.end();
    return flag;
}

void clear_shipping_mode()
{
    prefs.begin("runMode", false);
    prefs.remove("shipping");
    prefs.end();
}

void light_sleep()
{
    // gpio_hold_en(SLEEP_PIN);
    gpio_set_direction(SLEEP_PIN, GPIO_MODE_INPUT);
    gpio_wakeup_enable(gpio_num_t(SLEEP_PIN), GPIO_INTR_HIGH_LEVEL);
    esp_sleep_enable_gpio_wakeup();
    esp_light_sleep_start();
}

void deep_sleep()
{
    // esp_sleep_config_gpio_isolate();
    // esp_sleep_pd_config(ESP_PD_DOMAIN_RTC_PERIPH, ESP_PD_OPTION_ON);
    gpio_deep_sleep_hold_dis();
    gpio_set_direction(SLEEP_PIN, GPIO_MODE_INPUT);
    esp_deep_sleep_enable_gpio_wakeup(WAKEUP_PIN_BITMASK, ESP_GPIO_WAKEUP_GPIO_HIGH);
    esp_deep_sleep_start();
}