#include <Preferences.h>
#include <OneButton.h>
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include "driver/gpio.h"
#include "tuya_config.h"
#include "shipping.h"
#include "tuya_log.h"

// 按钮GPIO定义
#define BUTTON_GPIO6    GPIO_NUM_6
#define BUTTON_GPIO7    GPIO_NUM_7

// 全局变量
static OneButton button6(BUTTON_GPIO6, true, true);  // GPIO6, active LOW, enable pullup
static OneButton button7(BUTTON_GPIO7, true, true);  // GPIO7, active LOW, enable pullup
static volatile bool wakeup_requested = false;

Preferences prefs;

// 按钮事件回调函数
void button6_click() {
    TY_LOGI("Button GPIO6 clicked");
}

void button6_longpress_start() {
    TY_LOGI("Button GPIO6 long press started (3s) - requesting wakeup");
    wakeup_requested = true;
}

void button6_longpress_stop() {
    TY_LOGI("Button GPIO6 long press stopped");
}

void button7_click() {
    TY_LOGI("Button GPIO7 clicked");
}

void button7_longpress_start() {
    TY_LOGI("Button GPIO7 long press started (3s) - requesting wakeup");
    wakeup_requested = true;
}

void button7_longpress_stop() {
    TY_LOGI("Button GPIO7 long press stopped");
}

// 初始化按钮
void init_wakeup_buttons() {
    TY_LOGI("Initializing wakeup buttons on GPIO6 and GPIO7");

    // 配置按钮6事件 - 使用新的API
    button6.attachClick(button6_click);
    button6.attachLongPressStart(button6_longpress_start);
    button6.attachLongPressStop(button6_longpress_stop);
    button6.setPressMs(3000);              // 使用新的setPressMs设置3秒长按
    button6.setClickMs(50);                // 使用新的setClickMs设置点击时间
    button6.setDebounceMs(50);             // 使用新的setDebounceMs设置去抖时间

    // 配置按钮7事件 - 使用新的API
    button7.attachClick(button7_click);
    button7.attachLongPressStart(button7_longpress_start);
    button7.attachLongPressStop(button7_longpress_stop);
    button7.setPressMs(3000);              // 使用新的setPressMs设置3秒长按
    button7.setClickMs(50);                // 使用新的setClickMs设置点击时间
    button7.setDebounceMs(50);             // 使用新的setDebounceMs设置去抖时间

    wakeup_requested = false;
}

// 检查按钮状态（使用OneButton库）
bool check_wakeup_buttons() {
    button6.tick();
    button7.tick();
    return wakeup_requested;
}

// 获取按钮按下状态（使用OneButton库）
bool is_button6_pressed() {
    button6.tick();
    // OneButton库中检查按钮状态的方法
    return (digitalRead(BUTTON_GPIO6) == LOW);  // 按钮按下时为低电平
}

bool is_button7_pressed() {
    button7.tick();
    // OneButton库中检查按钮状态的方法
    return (digitalRead(BUTTON_GPIO7) == LOW);  // 按钮按下时为低电平
}

// 等待按钮释放（使用OneButton库）
void wait_button_release() {
    TY_LOGI("Waiting for button release...");
    unsigned long start_time = millis();

    while ((is_button6_pressed() || is_button7_pressed()) &&
           (millis() - start_time < 10000)) {  // 最多等待10秒
        button6.tick();
        button7.tick();
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    if (millis() - start_time >= 10000) {
        TY_LOGW("Button release timeout after 10 seconds");
    } else {
        TY_LOGI("Button released after %lu ms", millis() - start_time);
    }
}

// 非阻塞式检查长按状态（更好的实现）
bool check_long_press_wakeup_nonblocking() {
    TY_LOGI("Checking for long press wakeup (non-blocking)...");

    // 重置状态
    wakeup_requested = false;

    // 检查当前按钮状态
    button6.tick();
    button7.tick();

    // 如果有按钮被按下，等待长按事件触发
    if (is_button6_pressed() || is_button7_pressed()) {
        TY_LOGI("Button is pressed, waiting for long press event...");

        // 非阻塞等待：让OneButton库处理时间计算
        unsigned long wait_start = millis();
        while (!wakeup_requested && (millis() - wait_start < 4000)) {  // 等待最多4秒
            button6.tick();  // OneButton库内部处理时间和状态
            button7.tick();  // OneButton库内部处理时间和状态
            vTaskDelay(pdMS_TO_TICKS(10));  // 短暂延迟，避免CPU占用过高
        }

        if (wakeup_requested) {
            TY_LOGI("Long press confirmed by OneButton library");
            return true;
        } else {
            TY_LOGI("Long press timeout - no long press detected");
            return false;
        }
    } else {
        TY_LOGI("No button pressed");
        return false;
    }
}

// 立即检查是否已经有长按事件（用于已经触发的情况）
bool is_long_press_triggered() {
    button6.tick();
    button7.tick();
    return wakeup_requested;
}

// 重置唤醒请求状态
void reset_wakeup_request() {
    wakeup_requested = false;
}

void set_shipping_mode(bool flag)
{
    prefs.begin("runMode", false);
    prefs.putBool("shipping", flag);
    prefs.end();
}

bool should_enter_shipping_mode()
{
    prefs.begin("runMode", true);
    bool flag = prefs.getBool("shipping", false);
    prefs.end();
    return flag;
}

void clear_shipping_mode()
{
    prefs.begin("runMode", false);
    prefs.remove("shipping");
    prefs.end();
}

void light_sleep()
{
    TY_LOGI("Entering light sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 初始化按钮
    init_wakeup_buttons();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO6, GPIO_INTR_LOW_LEVEL);  // 按钮按下时为低电平

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO7, GPIO_INTR_LOW_LEVEL);  // 按钮按下时为低电平

    // 启用GPIO唤醒
    esp_sleep_enable_gpio_wakeup();

    // 进入轻度睡眠
    esp_light_sleep_start();

    // 唤醒后的处理
    esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();
    switch(wakeup_reason) {
        case ESP_SLEEP_WAKEUP_GPIO:
            TY_LOGI("Wakeup caused by GPIO");

            // 重新初始化按钮（唤醒后可能需要重新初始化）
            init_wakeup_buttons();

            // 使用改进的非阻塞长按检测
            if (check_long_press_wakeup_nonblocking()) {
                TY_LOGI("Long press confirmed via OneButton - exiting shipping mode");
                wait_button_release();  // 等待按钮释放
                clear_shipping_mode();
                return;
            } else {
                TY_LOGI("No long press detected - returning to sleep");
                reset_wakeup_request();
                light_sleep();  // 重新进入睡眠
                return;
            }
            break;
        default:
            TY_LOGI("Wakeup was not caused by GPIO");
            break;
    }
}

void deep_sleep()
{
    TY_LOGI("Entering deep sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 初始化按钮（确保GPIO配置正确）
    init_wakeup_buttons();

    // 禁用GPIO深度睡眠保持
    gpio_deep_sleep_hold_dis();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);

    // 设置GPIO唤醒位掩码 (GPIO6 = bit 6, GPIO7 = bit 7)
    uint64_t wakeup_pin_mask = (1ULL << BUTTON_GPIO6) | (1ULL << BUTTON_GPIO7);

    // 启用GPIO深度睡眠唤醒（低电平触发，因为按钮按下时为低电平）
    esp_deep_sleep_enable_gpio_wakeup(wakeup_pin_mask, ESP_GPIO_WAKEUP_GPIO_LOW);

    // 进入深度睡眠
    esp_deep_sleep_start();

    // 注意：深度睡眠唤醒后会重启系统，所以这里的代码不会执行
    // 唤醒后的处理需要在setup()函数中检查唤醒原因
}

// 深度睡眠唤醒后的按钮检查（在main.cpp的setup中调用）
bool check_deep_sleep_wakeup() {
    TY_LOGI("Checking deep sleep wakeup with OneButton...");

    // 初始化按钮
    init_wakeup_buttons();

    // 短暂延迟让系统稳定
    vTaskDelay(pdMS_TO_TICKS(100));

    // 检查是否有按钮被按下
    if (is_button6_pressed() || is_button7_pressed()) {
        TY_LOGI("Button still pressed after deep sleep wakeup");

        // 使用改进的非阻塞长按检测
        if (check_long_press_wakeup_nonblocking()) {
            TY_LOGI("Long press confirmed after deep sleep - clearing shipping mode");
            wait_button_release();
            return true;  // 确认长按，应该退出shipping模式
        } else {
            TY_LOGI("Short press after deep sleep - should return to sleep");
            return false;  // 短按，应该重新进入睡眠
        }
    } else {
        TY_LOGI("No button pressed after deep sleep wakeup - clearing shipping mode");
        return true;  // 没有按钮按下，正常退出shipping模式
    }
}