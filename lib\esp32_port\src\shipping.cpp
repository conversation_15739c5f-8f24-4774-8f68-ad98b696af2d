#include <Preferences.h>
#include <OneButton.h>
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include "driver/gpio.h"
#include "tuya_config.h"
#include "shipping.h"
#include "tuya_log.h"

// 按钮GPIO定义
#define BUTTON_GPIO6    GPIO_NUM_6
#define BUTTON_GPIO7    GPIO_NUM_7

// 全局变量
static OneButton button6(BUTTON_GPIO6, true, true);  // GPIO6, active LOW, enable pullup
static OneButton button7(BUTTON_GPIO7, true, true);  // GPIO7, active LOW, enable pullup
static volatile bool wakeup_requested = false;

Preferences prefs;

// 按钮长按回调函数
void button6_longpress() {
    TY_LOGI("Button GPIO6 long pressed (3s) - requesting wakeup");
    wakeup_requested = true;
}

void button7_longpress() {
    TY_LOGI("Button GPIO7 long pressed (3s) - requesting wakeup");
    wakeup_requested = true;
}

// 初始化按钮
void init_wakeup_buttons() {
    TY_LOGI("Initializing wakeup buttons on GPIO6 and GPIO7");

    // 配置按钮长按事件（3秒）
    button6.attachLongPressStart(button6_longpress);
    button6.setLongPressIntervalMs(3000);  // 3秒长按

    button7.attachLongPressStart(button7_longpress);
    button7.setLongPressIntervalMs(3000);  // 3秒长按

    wakeup_requested = false;
}

// 检查按钮状态（在休眠前调用）
bool check_wakeup_buttons() {
    button6.tick();
    button7.tick();
    return wakeup_requested;
}

void set_shipping_mode(bool flag)
{
    prefs.begin("runMode", false);
    prefs.putBool("shipping", flag);
    prefs.end();
}

bool should_enter_shipping_mode()
{
    prefs.begin("runMode", true);
    bool flag = prefs.getBool("shipping", false);
    prefs.end();
    return flag;
}

void clear_shipping_mode()
{
    prefs.begin("runMode", false);
    prefs.remove("shipping");
    prefs.end();
}

void light_sleep()
{
    TY_LOGI("Entering light sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 初始化按钮
    init_wakeup_buttons();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO6, GPIO_INTR_LOW_LEVEL);  // 按钮按下时为低电平

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO7, GPIO_INTR_LOW_LEVEL);  // 按钮按下时为低电平

    // 启用GPIO唤醒
    esp_sleep_enable_gpio_wakeup();

    // 进入轻度睡眠
    esp_light_sleep_start();

    // 唤醒后的处理
    esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();
    switch(wakeup_reason) {
        case ESP_SLEEP_WAKEUP_GPIO:
            TY_LOGI("Wakeup caused by GPIO");
            // 检查是否是长按唤醒
            vTaskDelay(pdMS_TO_TICKS(100));  // 短暂延迟让按钮稳定

            // 检查按钮状态，确认是否为长按
            unsigned long start_time = millis();
            bool button6_pressed = (digitalRead(BUTTON_GPIO6) == LOW);
            bool button7_pressed = (digitalRead(BUTTON_GPIO7) == LOW);

            if (button6_pressed || button7_pressed) {
                TY_LOGI("Button still pressed, checking for long press...");
                // 等待按钮释放或达到3秒
                while ((digitalRead(BUTTON_GPIO6) == LOW || digitalRead(BUTTON_GPIO7) == LOW) &&
                       (millis() - start_time < 3000)) {
                    vTaskDelay(pdMS_TO_TICKS(10));
                }

                if (millis() - start_time >= 3000) {
                    TY_LOGI("Long press confirmed - exiting shipping mode");
                    clear_shipping_mode();
                    return;
                } else {
                    TY_LOGI("Short press detected - returning to sleep");
                    light_sleep();  // 重新进入睡眠
                    return;
                }
            }
            break;
        default:
            TY_LOGI("Wakeup was not caused by GPIO");
            break;
    }
}

void deep_sleep()
{
    TY_LOGI("Entering deep sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 禁用GPIO深度睡眠保持
    gpio_deep_sleep_hold_dis();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);

    // 设置GPIO唤醒位掩码 (GPIO6 = bit 6, GPIO7 = bit 7)
    uint64_t wakeup_pin_mask = (1ULL << BUTTON_GPIO6) | (1ULL << BUTTON_GPIO7);

    // 启用GPIO深度睡眠唤醒（低电平触发，因为按钮按下时为低电平）
    esp_deep_sleep_enable_gpio_wakeup(wakeup_pin_mask, ESP_GPIO_WAKEUP_GPIO_LOW);

    // 进入深度睡眠
    esp_deep_sleep_start();

    // 注意：深度睡眠唤醒后会重启系统，所以这里的代码不会执行
    // 唤醒后的处理需要在setup()函数中检查唤醒原因
}