/**
 * @file lv_tjpgd.h
 *
 */

#ifndef LV_TJPGD_H
#define LV_TJPGD_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#if LV_USE_TJPGD

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

void lv_tjpgd_init(void);

void lv_tjpgd_deinit(void);

/**********************
 *      MACROS
 **********************/

#endif /*LV_USE_TJPGD*/

#ifdef __cplusplus
}
#endif

#endif /* LV_TJPGD_H */
