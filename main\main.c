/**
 * @file main.c
 * @brief ESP-IDF main entry point for Tuya IoT application
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"

// External function from tuya_main.c
extern void tuya_app_main(void);

static const char *TAG = "main";

void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-C3 Tuya IoT Application Starting...");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    ESP_LOGI(TAG, "NVS initialized successfully");
    
    // Start Tuya application
    ESP_LOGI(TAG, "Starting Tuya application...");
    tuya_app_main();
    
    ESP_LOGI(TAG, "Tuya application started");
    
    // Keep the main task alive
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}
