/**
 * @file tuya_config_manager.c
 * @brief Tuya configuration manager with NVS storage support
 */

#include "tuya_config_manager.h"
#include "tuya_config.h"
#include "storage_interface.h"
#include "tuya_log.h"
#include "tuya_config.h"
#include <string.h>
#include <stdlib.h>

static const char *TAG = "TUYA_CONFIG_MGR";

// NVS键名定义
#define NVS_KEY_PRODUCT_KEY     "product_key"
#define NVS_KEY_DEVICE_UUID     "device_uuid"
#define NVS_KEY_DEVICE_AUTHKEY  "device_authkey"
#define NVS_KEY_SOFTWARE_VER    "software_ver"

// 静态存储配置数据
static tuya_config_data_t g_config_data = {0};
static bool g_config_loaded = false;

static int read_string_from_nvs(const char *key, char *buffer, size_t buffer_size, const char *default_value)
{
    size_t data_len = buffer_size - 1; // 留出空间给null终止符
    int ret = local_storage_get(key, (uint8_t*)buffer, &data_len);
    
    if (ret == OPRT_OK && data_len > 0) {
        buffer[data_len] = '\0'; // 确保字符串以null结尾
        TY_LOGI("Read from NVS - %s: %s", key, buffer);
        return OPRT_OK;
    } else {
        // 使用默认值
        if (default_value) {
            strncpy(buffer, default_value, buffer_size - 1);
            buffer[buffer_size - 1] = '\0';
            TY_LOGI("Using default - %s: %s", key, buffer);
        } else {
            buffer[0] = '\0';
            TY_LOGW("No value found for %s and no default provided", key);
        }
        return ret;
    }
}

static int write_string_to_nvs(const char *key, const char *value)
{
    if (!key || !value) {
        return OPRT_INVALID_PARM;
    }
    
    size_t value_len = strlen(value);
    int ret = local_storage_set(key, (const uint8_t*)value, value_len);
    
    if (ret == OPRT_OK) {
        TY_LOGI("Saved to NVS - %s: %s", key, value);
    } else {
        TY_LOGE("Failed to save to NVS - %s: %d", key, ret);
    }
    
    return ret;
}

int tuya_config_load(void)
{
    TY_LOGI("Loading Tuya configuration from NVS...");
    
    // 读取产品密钥
    read_string_from_nvs(NVS_KEY_PRODUCT_KEY, 
                        g_config_data.product_key, 
                        sizeof(g_config_data.product_key),
                        NULL);
    
    // 读取设备UUID
    read_string_from_nvs(NVS_KEY_DEVICE_UUID, 
                        g_config_data.device_uuid, 
                        sizeof(g_config_data.device_uuid),
                        NULL);
    
    // 读取设备认证密钥
    read_string_from_nvs(NVS_KEY_DEVICE_AUTHKEY, 
                        g_config_data.device_authkey, 
                        sizeof(g_config_data.device_authkey),
                        NULL);
    
    // 读取软件版本
    read_string_from_nvs(NVS_KEY_SOFTWARE_VER, 
                        g_config_data.software_ver, 
                        sizeof(g_config_data.software_ver),
                        SOFTWARE_VER);
    
    g_config_loaded = true;
    
    TY_LOGI("=== Loaded Configuration ===");
    TY_LOGI("Product Key: %s", g_config_data.product_key);
    TY_LOGI("Device UUID: %s", g_config_data.device_uuid);
    TY_LOGI("Device AuthKey: %s", g_config_data.device_authkey);
    TY_LOGI("Software Ver: %s", g_config_data.software_ver);
    TY_LOGI("============================");
    
    return OPRT_OK;
}

int tuya_config_save(const tuya_config_data_t *config)
{
    if (!config) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Saving Tuya configuration to NVS...");
    
    int ret = OPRT_OK;
    
    // 保存产品密钥
    if (strlen(config->product_key) > 0) {
        ret = write_string_to_nvs(NVS_KEY_PRODUCT_KEY, config->product_key);
        if (ret != OPRT_OK) return ret;
    }
    
    // 保存设备UUID
    if (strlen(config->device_uuid) > 0) {
        ret = write_string_to_nvs(NVS_KEY_DEVICE_UUID, config->device_uuid);
        if (ret != OPRT_OK) return ret;
    }
    
    // 保存设备认证密钥
    if (strlen(config->device_authkey) > 0) {
        ret = write_string_to_nvs(NVS_KEY_DEVICE_AUTHKEY, config->device_authkey);
        if (ret != OPRT_OK) return ret;
    }
    
    // 保存软件版本
    if (strlen(config->software_ver) > 0) {
        ret = write_string_to_nvs(NVS_KEY_SOFTWARE_VER, config->software_ver);
        if (ret != OPRT_OK) return ret;
    }
    
    // 更新内存中的配置
    memcpy(&g_config_data, config, sizeof(tuya_config_data_t));
    g_config_loaded = true;
    
    TY_LOGI("Configuration saved successfully");
    return OPRT_OK;
}

const tuya_config_data_t* tuya_config_get(void)
{
    if (!g_config_loaded) {
        TY_LOGW("Configuration not loaded, loading now...");
        tuya_config_load();
    }
    
    return &g_config_data;
}

int tuya_config_update_prodkey(const char *new_prodkey)
{
    if (!new_prodkey || strlen(new_prodkey) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device PRODKEY: %s", new_prodkey);
    
    strncpy(g_config_data.product_key, new_prodkey, sizeof(g_config_data.product_key) - 1);
    g_config_data.product_key[sizeof(g_config_data.product_key) - 1] = '\0';
    
    return write_string_to_nvs(NVS_KEY_PRODUCT_KEY, new_prodkey);
}

int tuya_config_update_uuid(const char *new_uuid)
{
    if (!new_uuid || strlen(new_uuid) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device UUID: %s", new_uuid);
    
    strncpy(g_config_data.device_uuid, new_uuid, sizeof(g_config_data.device_uuid) - 1);
    g_config_data.device_uuid[sizeof(g_config_data.device_uuid) - 1] = '\0';
    
    return write_string_to_nvs(NVS_KEY_DEVICE_UUID, new_uuid);
}

int tuya_config_update_authkey(const char *new_authkey)
{
    if (!new_authkey || strlen(new_authkey) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device AuthKey: %s", new_authkey);
    
    strncpy(g_config_data.device_authkey, new_authkey, sizeof(g_config_data.device_authkey) - 1);
    g_config_data.device_authkey[sizeof(g_config_data.device_authkey) - 1] = '\0';
    
    return write_string_to_nvs(NVS_KEY_DEVICE_AUTHKEY, new_authkey);
}

int tuya_config_reset_to_defaults(void)
{
    TY_LOGI("Resetting configuration to defaults...");
    
    // 删除NVS中的配置
    local_storage_del(NVS_KEY_PRODUCT_KEY);
    local_storage_del(NVS_KEY_DEVICE_UUID);
    local_storage_del(NVS_KEY_DEVICE_AUTHKEY);
    local_storage_del(NVS_KEY_SOFTWARE_VER);
    
    // 重新加载默认配置
    g_config_loaded = false;
    return tuya_config_load();
}

void tuya_config_print_current(void)
{
    const tuya_config_data_t *config = tuya_config_get();
    
    TY_LOGI("=== Current Tuya Configuration ===");
    TY_LOGI("Product Key: %s", config->product_key);
    TY_LOGI("Device UUID: %s", config->device_uuid);
    TY_LOGI("Device AuthKey: %s", config->device_authkey);
    TY_LOGI("Software Ver: %s", config->software_ver);
    TY_LOGI("==================================");
}
