/**
 * @file tuya_config_cli.h
 * @brief Tuya configuration CLI commands header
 */

#ifndef TUYA_CONFIG_CLI_H
#define TUYA_CONFIG_CLI_H

#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化配置CLI（包含UART和任务）
 */
void tuya_config_cli_init(void);

/**
 * @brief 反初始化配置CLI
 */
void tuya_config_cli_deinit(void);

/**
 * @brief 检查CLI是否已初始化
 * @return true 已初始化，false 未初始化
 */
bool tuya_config_cli_is_initialized(void);

/**
 * @brief 处理CLI命令
 * @param command 命令字符串
 */
void tuya_config_cli_process_command(const char *command);

/**
 * @brief 打印帮助信息
 */
void tuya_config_cli_print_help(void);

/**
 * @brief 设置测试配置
 */
void tuya_config_cli_set_test_config(void);

/**
 * @brief 发送响应到CLI
 * @param response 响应字符串
 */
void tuya_config_cli_send_response(const char *response);

/**
 * @brief 重启设备
 * @note 此函数会在3秒倒计时后重启设备
 */
void tuya_config_cli_restart_device(void);

#ifdef __cplusplus
}
#endif

#endif /* TUYA_CONFIG_CLI_H */
