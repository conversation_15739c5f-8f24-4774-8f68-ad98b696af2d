#ifdef __cplusplus
extern "C"
{
#endif

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include "tuya_error_code.h"
#include "storage_interface.h"
#include "system_interface.h"
#include "nvs_flash.h"
#include "nvs.h"
//#include "esp_log.h"
#include "tuya_log.h"

#define STORAGE_NAMESPACE "tuya"
    nvs_handle_t storage_handle;

    int local_storage_init(void)
    {
        esp_err_t err;

        // 初始化NVS flash
        err = nvs_flash_init();
        if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
            // NVS partition was truncated and needs to be erased
            ESP_ERROR_CHECK(nvs_flash_erase());
            err = nvs_flash_init();
        }
        ESP_ERROR_CHECK(err);

        err = nvs_open(STORAGE_NAMESPACE, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK) {
            TY_LOGE("Error (%s) opening NVS handle!", esp_err_to_name(err));
            return err;
        }

        // 显示NVS统计信息
        nvs_stats_t stats;
        err = nvs_get_stats(NULL, &stats);
        if (err == ESP_OK) {
            TY_LOGI("=== NVS Statistics ===");
            TY_LOGI("Used entries: %3zu", stats.used_entries);
            TY_LOGI("Free entries: %3zu", stats.free_entries);
            TY_LOGI("Total entries: %3zu", stats.total_entries);
            TY_LOGI("Namespace count: %3zu", stats.namespace_count);
            TY_LOGI("=====================");
        }

        // 遍历并输出所有key-value对
        TY_LOGI("=== Reading all NVS entries in namespace '%s' ===", STORAGE_NAMESPACE);

        nvs_iterator_t it = NULL;
        it = nvs_entry_find("nvs", STORAGE_NAMESPACE, NVS_TYPE_ANY);

        if (it == NULL) {
            TY_LOGI("No entries found in namespace: %s", STORAGE_NAMESPACE);
        } else {
            int entry_count = 0;

            while (it != NULL) {
                nvs_entry_info_t info;
                nvs_entry_info(it, &info);

                entry_count++;
                TY_LOGI("--- Entry %d ---", entry_count);
                TY_LOGI("Key: %s", info.key);
                TY_LOGI("Type: %d", info.type);

                // 根据不同类型读取并显示值
                switch (info.type) {
                    case NVS_TYPE_U8: {
                        uint8_t value;
                        err = nvs_get_u8(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (u8): %u", value);
                        }
                        break;
                    }
                    case NVS_TYPE_I8: {
                        int8_t value;
                        err = nvs_get_i8(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (i8): %d", value);
                        }
                        break;
                    }
                    case NVS_TYPE_U16: {
                        uint16_t value;
                        err = nvs_get_u16(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (u16): %u", value);
                        }
                        break;
                    }
                    case NVS_TYPE_I16: {
                        int16_t value;
                        err = nvs_get_i16(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (i16): %d", value);
                        }
                        break;
                    }
                    case NVS_TYPE_U32: {
                        uint32_t value;
                        err = nvs_get_u32(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (u32): %lu", (unsigned long)value);
                        }
                        break;
                    }
                    case NVS_TYPE_I32: {
                        int32_t value;
                        err = nvs_get_i32(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (i32): %ld", (long)value);
                        }
                        break;
                    }
                    case NVS_TYPE_U64: {
                        uint64_t value;
                        err = nvs_get_u64(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (u64): %llu", (unsigned long long)value);
                        }
                        break;
                    }
                    case NVS_TYPE_I64: {
                        int64_t value;
                        err = nvs_get_i64(storage_handle, info.key, &value);
                        if (err == ESP_OK) {
                            TY_LOGI("Value (i64): %lld", (long long)value);
                        }
                        break;
                    }
                    case NVS_TYPE_STR: {
                        size_t required_size = 0;
                        err = nvs_get_str(storage_handle, info.key, NULL, &required_size);
                        if (err == ESP_OK && required_size > 0) {
                            char* str_value = malloc(required_size);
                            if (str_value != NULL) {
                                err = nvs_get_str(storage_handle, info.key, str_value, &required_size);
                                if (err == ESP_OK) {
                                    TY_LOGI("Value (string): %s", str_value);
                                }
                                free(str_value);
                            }
                        }
                        break;
                    }
                    case NVS_TYPE_BLOB: {
                        size_t required_size = 0;
                        err = nvs_get_blob(storage_handle, info.key, NULL, &required_size);
                        if (err == ESP_OK && required_size > 0) {
                            TY_LOGI("Value (blob): %zu bytes", required_size);

                            // 读取blob数据并以十六进制显示前32字节
                            uint8_t* blob_value = malloc(required_size);
                            if (blob_value != NULL) {
                                err = nvs_get_blob(storage_handle, info.key, blob_value, &required_size);
                                if (err == ESP_OK) {
                                    TY_LOGI("Blob data (hex, first %zu bytes):",
                                           required_size > 32 ? 32 : required_size);

                                    // 以十六进制格式输出
                                    char hex_str[256] = {0};
                                    char temp[4];
                                    size_t display_size = required_size > 32 ? 32 : required_size;

                                    for (size_t i = 0; i < display_size; i++) {
                                        snprintf(temp, sizeof(temp), "%02X ", blob_value[i]);
                                        strcat(hex_str, temp);
                                        if ((i + 1) % 16 == 0) {
                                            TY_LOGI("  %s", hex_str);
                                            hex_str[0] = '\0';
                                        }
                                    }
                                    if (strlen(hex_str) > 0) {
                                        TY_LOGI("  %s", hex_str);
                                    }

                                    if (required_size > 32) {
                                        TY_LOGI("  ... (%zu more bytes)", required_size - 32);
                                    }
                                }
                                free(blob_value);
                            }
                        }
                        break;
                    }
                    default:
                        TY_LOGI("Value: Unknown type (%d)", info.type);
                        break;
                }

                if (err != ESP_OK) {
                    TY_LOGE("Error reading value for key '%s': %s", info.key, esp_err_to_name(err));
                }

                it = nvs_entry_next(it);
            }

            nvs_release_iterator(it);
            TY_LOGI("=== Total entries found: %d ===", entry_count);
        }

        nvs_close(storage_handle);
        TY_LOGI("NVS storage initialized successfully");
        return OPRT_OK;
    }

    int local_storage_clear(void)
    {
        TY_LOGE("local_storage_clear");
        esp_err_t err = nvs_flash_erase();
        if (err != ESP_OK)
        {
            TY_LOGE("nvs_flash_erase failed: 0x%x", err);
            return err;
        }
        return OPRT_OK;
    }
    int local_storage_set(const char *key, const uint8_t *buffer, size_t length)
    {
        if (NULL == key || NULL == buffer)
        {
            return OPRT_INVALID_PARM;
        }

        log_debug("set key:%s", key);

        esp_err_t err;

        err = nvs_open(STORAGE_NAMESPACE, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_set_blob(storage_handle, key, buffer, length);
        TY_LOGE("local_storage_set %s %d bytes", key, length);
        if (err != ESP_OK)
            return err;

        // Commit
        err = nvs_commit(storage_handle);
        if (err != ESP_OK)
            return err;

        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

    int local_storage_get(const char *key, uint8_t *buffer, size_t *length)
    {
        if (NULL == key || NULL == buffer || NULL == length)
        {
            return OPRT_INVALID_PARM;
        }

        log_debug("get key:%s, len:%d", key, (int)*length);

        esp_err_t err;

        err = nvs_open(STORAGE_NAMESPACE, NVS_READONLY, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_get_blob(storage_handle, key, NULL, length);
        TY_LOGI("local_storage_get %s %d bytes", key, *length);
        if (err == ESP_ERR_NVS_NOT_FOUND)
        {
            return err;
        }
        log_debug("get key:%s, xlen:%d", key, *length);

        err = nvs_get_blob(storage_handle, key, buffer, length);
        TY_LOGI("local_storage_get %s %d bytes", key, *length);
        if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND)
            return err;

        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

    int local_storage_del(const char *key)
    {
        log_debug("key:%s", key);

        esp_err_t err;

        err = nvs_open(STORAGE_NAMESPACE, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_erase_key(storage_handle, key);
        TY_LOGE("local_storage_del: %s", key);
        if (err != ESP_OK)
            return err;

        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

#ifdef __cplusplus
}
#endif
