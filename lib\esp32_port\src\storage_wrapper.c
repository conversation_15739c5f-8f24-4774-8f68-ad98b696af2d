#ifdef __cplusplus
extern "C"
{
#endif

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include "tuya_error_code.h"
#include "storage_interface.h"
#include "system_interface.h"
#include "nvs_flash.h"
#include "nvs.h"
//#include "esp_log.h"
#include "tuya_log.h"
#include "tuya_config.h"


    nvs_handle_t storage_handle;

    int local_storage_init(const char *storage_name)
    {
        esp_err_t err;

        // 初始化NVS flash
        err = nvs_flash_init();
#if 0
        if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
            // NVS partition was truncated and needs to be erased
            ESP_ERROR_CHECK(nvs_flash_erase());
            err = nvs_flash_init();
        }
#endif
        ESP_ERROR_CHECK(err);

        err = nvs_open(storage_name, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK) {
            TY_LOGE("Error (%s) opening NVS handle!", esp_err_to_name(err));
            return err;
        }

        // 显示NVS统计信息
        nvs_stats_t stats;
        err = nvs_get_stats(NULL, &stats);
        if (err == ESP_OK) {
            TY_LOGI(
                "Used entries: %3zu\t"
                "Free entries: %3zu\t"
                "Total entries: %3zu\t"
                "Namespace count: %3zu\n",
                stats.used_entries,
                stats.free_entries,
                stats.total_entries,
                stats.namespace_count);
                                    }

        // 简化初始化，移除可能导致问题的迭代器代码
        nvs_close(storage_handle);
        TY_LOGI("NVS storage initialized successfully");
        return OPRT_OK;
    }

    int local_storage_clear(const char *storage_name)
    {
        TY_LOGE("local_storage_clear");
        esp_err_t err = nvs_flash_erase();
        if (err != ESP_OK)
        {
            TY_LOGE("nvs_flash_erase failed: 0x%x", err);
            return err;
        }
        return OPRT_OK;
    }
    int local_storage_set(const char *storage_name, const char *key, const uint8_t *buffer, size_t length)
    {
        if (NULL == key || NULL == buffer)
        {
            return OPRT_INVALID_PARM;
        }

        TY_LOGD("set key:%s", key);

        esp_err_t err;

        err = nvs_open(storage_name, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_set_blob(storage_handle, key, buffer, length);
        TY_LOGE("local_storage_set %s %d bytes", key, length);
        if (err != ESP_OK)
            return err;

        // Commit
        err = nvs_commit(storage_handle);
        if (err != ESP_OK)
            return err;

        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

    int local_storage_get(const char *storage_name, const char *key, uint8_t *buffer, size_t *length)
    {
        if (NULL == key || NULL == buffer || NULL == length)
        {
            return OPRT_INVALID_PARM;
        }

        TY_LOGD("get key:%s, len:%d", key, (int)*length);

        esp_err_t err;

        err = nvs_open(storage_name, NVS_READONLY, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_get_blob(storage_handle, key, NULL, length);
        TY_LOGD("local_storage_get %s %d bytes", key, *length);
        if (err == ESP_ERR_NVS_NOT_FOUND)
        {
            return err;
        }
        TY_LOGD("get key:%s, xlen:%d", key, *length);

        err = nvs_get_blob(storage_handle, key, buffer, length);
        buffer[*length] = '\0';  // 确保字符串以null结尾
        TY_LOGD("local_storage_get %s %d bytes", key, *length);
        if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND)
            return err;

        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

    int local_storage_del(const char *storage_name, const char *key)
    {
        TY_LOGD("key:%s", key);

        esp_err_t err;

        err = nvs_open(storage_name, NVS_READWRITE, &storage_handle);
        if (err != ESP_OK)
            return err;

        err = nvs_erase_key(storage_handle, key);
        TY_LOGE("local_storage_del: %s", key);
        if (err != ESP_OK)
            return err;

        err = nvs_commit(storage_handle);
        if (err != ESP_OK)
            return err;
        // Close
        nvs_close(storage_handle);

        return OPRT_OK;
    }

#ifdef __cplusplus
}
#endif
