#pragma once

// 通用定义，定义ADC通道和衰减，应该在调用代码块里面定义，此处仅为示例
static float voltage_divider_factor = 1.00;   // 电压分压系数
//单次采样
static uint8_t adc_pin = 4;
static adc_unit_t adc_unit = ADC_UNIT_1;
static adc1_channel_t adc_channel = ADC1_CHANNEL_4;
static adc_atten_t adc_atten = ADC_ATTEN_DB_12;    // 0mV ~ 2500mV/0 mV ~ 3100 mV 衰减范围
static adc_bits_width_t adc_width = ADC_WIDTH_BIT_12;  // 12位分辨率
static const uint16_t default_vref = 1100;   // 默认参考电压，单位mV
//连续采样
static uint16_t adc1_chan_mask = BIT(4);
static uint16_t adc2_chan_mask = 0x0;   //example for ADC2, BIT(0)
static adc_channel_t channel[1] = {(adc_channel_t)ADC1_CHANNEL_4}; //example for ADC2,(ADC2_CHANNEL_0 | 1 << 3)

void adc_calibration(adc_unit_t unit, adc_atten_t atten, adc_bits_width_t width, uint16_t default_vref);
void adc_single_init(adc1_channel_t channel, adc_atten_t atten, adc_bits_width_t width);
void adc_continuous_init(uint16_t adc1_chan_mask, uint16_t adc2_chan_mask, adc_channel_t *channel, adc_atten_t atten);
float ReadVoltageContinuous(uint16_t adc1_chan_mask, uint16_t adc2_chan_mask);
float ReadVoltageSingle(uint8_t pin, adc1_channel_t channel, adc_atten_t atten, adc_bits_width_t width);
