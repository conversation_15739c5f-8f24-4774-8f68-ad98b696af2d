/**
 * @file tuya_config_manager.h
 * @brief Tuya configuration manager with NVS storage support
 */

#ifndef TUYA_CONFIG_MANAGER_H
#define TUYA_CONFIG_MANAGER_H

#include "tuya_error_code.h"

#ifdef __cplusplus
extern "C" {
#endif

// 配置数据结构
typedef struct {
    char product_key[32];      // 产品密钥
    char device_uuid[32];      // 设备UUID
    char device_authkey[64];   // 设备认证密钥
    char software_ver[16];     // 软件版本
} tuya_config_data_t;

/**
 * @brief 从NVS加载Tuya配置，如果不存在则使用默认值
 * @return OPRT_OK 成功，其他值表示错误
 */
int tuya_config_load(void);

/**
 * @brief 保存Tuya配置到NVS
 * @param config 要保存的配置数据
 * @return OPRT_OK 成功，其他值表示错误
 */
int tuya_config_save(const tuya_config_data_t *config);

/**
 * @brief 获取当前的Tuya配置
 * @return 指向配置数据的指针
 */
const tuya_config_data_t* tuya_config_get(void);

int tuya_config_update_prodkey(const char *new_prodkey);
/**
 * @brief 更新设备UUID
 * @param new_uuid 新的UUID
 * @return OPRT_OK 成功，其他值表示错误
 */
int tuya_config_update_uuid(const char *new_uuid);

/**
 * @brief 更新设备认证密钥
 * @param new_authkey 新的认证密钥
 * @return OPRT_OK 成功，其他值表示错误
 */
int tuya_config_update_authkey(const char *new_authkey);

/**
 * @brief 重置配置为默认值
 * @return OPRT_OK 成功，其他值表示错误
 */
int tuya_config_reset_to_defaults(void);

/**
 * @brief 打印当前配置
 */
void tuya_config_print_current(void);

#ifdef __cplusplus
}
#endif

#endif /* TUYA_CONFIG_MANAGER_H */
