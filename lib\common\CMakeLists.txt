##
# @file CMakeLists.txt
# @brief Common library component for ESP-IDF
#/

# Get all source files
file(GLOB_RECURSE component_srcs
    "${CMAKE_CURRENT_SOURCE_DIR}/utilities/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/backoffAlgorithm/source/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/source/*.c"
)

# Set include directories
set(component_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
    "${CMAKE_CURRENT_SOURCE_DIR}/backoffAlgorithm/source/include"
    "${CMAKE_CURRENT_SOURCE_DIR}/utilities"
)

# Register ESP-IDF component
idf_component_register(
    SRCS ${component_srcs}
    INCLUDE_DIRS ${component_includes}
    REQUIRES
        freertos
        lwip
)


