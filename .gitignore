.pio
.vscode/.browse.c_cpp.db*
.vscode/c_cpp_properties.json
.vscode/launch.json
.vscode/ipch
*.o
libmbed*
*.sln
*.vcxproj
settings.json
__pycache__
.vscode
.tags

CMakeCache.txt
/CMakeFiles

/tmp
/.cache
/cache
/output
/build
tuyadb/
/platform/*/
__pycache__
.build
*.using_config
.mirror

/arduino-tuyaopen
/tools/tyutool/*tyutool_*
/tools/tyutool/cache/
/.set_example
###

#define TUYA_PRODUCT_KEY      "ff1lwoe4t5rkeg5m" // for test
#define TUYA_DEVICE_UUID      "tuyac0828c3dfbc6a170"
#define TUYA_DEVICE_AUTHKEY   "gxQPs3qtR0pNSx12hh0floVG117uUnJL"

__pycache__
.vscode
.tags

CMakeCache.txt
/CMakeFiles

/tmp
/.cache
/cache
/output
/build
tuyadb/
/platform/*/
__pycache__
.build
*.using_config
.mirror

/arduino-tuyaopen
/tools/tyutool/*tyutool_*
/tools/tyutool/cache/
/.set_example
