/**
 * @file tuya_config.h
 * @brief IoT specific configuration file
 *
 * @copyright Copyright (c) 2021-2024 Tuya Inc. All Rights Reserved.
 */

#ifndef TUYA_CONFIG_H_
#define TUYA_CONFIG_H_

/**
 * @brief configure the product information
 *
 * TUYA_PRODUCT_KEY: PID, create on the Tuya IoT platform
 * TUYA_DEVICE_UUID: UUID, create on the Tuya IoT platform
 * TUYA_DEVICE_AUTHKEY: AUTHKEY, create on the Tuya IoT platform
 *
 * detail please refer to:
 * 1. Create the product and get the pid:
 * https://developer.tuya.com/cn/docs/iot-device-dev/application-creation?id=Kbxw7ket3aujc
 * 2. Get the open-sdk license code or module: https://platform.tuya.com/purchase/index?type=6
 *
 * warning: please replace these production information with your product key
 * and license, otherwise the demo cannot work.
 *
 */
// clang-format off
#define TUYA_PRODUCT_KEY      "f0umqyugotpk67t6"                        // Please change your product key
#define TUYA_OPENSDK_UUID      "2697c555a5624f71564sua"                    // Please change the correct uuid
#define TUYA_OPENSDK_AUTHKEY   "JglARRgMazew3oLU"        // Please change the correct authkey

/**
 * @brief PINCODE for AP provisioning
 * 
 * TUYA_NETCFG_PINCODE: a random PINCODE for AP provisioning, PINCODE was generated BY TUYA PMS system!!!
 * 
 * WARNING: PINCODE is mandatory for AP provisioning
 */
// #define TUYA_NETCFG_PINCODE   "69832860"

// clang-format on

#endif
