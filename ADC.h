/**
 * @file ADC.h
 * @brief ESP32-C3 Hardware ADC Sampling Header
 * 
 * This header provides functions for hardware-accelerated ADC sampling
 * using ESP32-C3's timer-based sampling instead of software loops.
 */

#ifndef ADC_H
#define ADC_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize ADC hardware sampling system
 * 
 * This function configures:
 * - ADC1 Channel 4 (GPIO4) with 12-bit resolution
 * - 11dB attenuation for 0-2500mV range
 * - Hardware timer for precise sampling intervals
 * - ADC calibration characteristics
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t adc_hardware_init(void);

/**
 * @brief Read voltage using hardware timer-based sampling
 * 
 * This function:
 * - Uses hardware timer interrupts for precise sampling timing
 * - Collects 64 samples at 20kHz sampling rate
 * - Calculates average to reduce noise
 * - Applies ADC calibration for accurate voltage reading
 * 
 * @return Voltage in volts (V), 0.0 on error
 */
float ReadVoltageHardware(void);

/**
 * @brief Read voltage using traditional software loop sampling
 * 
 * This function provides the original software-based sampling
 * for comparison with hardware sampling.
 * 
 * @return Voltage in volts (V), 0.0 on error
 */
float ReadVoltageSoftware(void);

/**
 * @brief Deinitialize ADC hardware sampling system
 * 
 * Cleans up timer resources and disables interrupts.
 */
void adc_hardware_deinit(void);

/**
 * @brief Arduino-style setup function
 * 
 * Initializes the ADC hardware sampling system.
 * Call this once at startup.
 */
void setup(void);

/**
 * @brief Arduino-style loop function
 * 
 * Continuously reads voltage using hardware sampling
 * and prints results every second.
 */
void loop(void);

#ifdef __cplusplus
}
#endif

#endif /* ADC_H */
