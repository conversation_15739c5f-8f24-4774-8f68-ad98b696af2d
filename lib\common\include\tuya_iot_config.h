/**
 * @file tuya_iot_config.h
 * @brief Defines the error codes used in the Tuya SDK.
 *
 * This file is the configuration file for the Tuya IoT Platform SDK. It
 * contains the basic configuration information required for the SDK to run, and
 * is automatically generated by the Tuya cross-platform build system. It should
 * not be manually modified.
 *
 * @copyright Copyright (c) 2021-2024 Tuya Inc. All Rights Reserved.
 */

#ifndef TUYA_IOT_CONFIG_H
#define TUYA_IOT_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

// clang-format off

/* default definitons */

#define SYSTEM_SMALL_MEMORY_BEGIN 0 /*small memory systems begin */
#define SYSTEM_REALTEK8710_1M     1
#define SYSTEM_REALTEK8710_2M     2
#define SYSTEM_NON_OS             3 /*no operating system*/
#define SYSTEM_FREERTOS           98
#define SYSTEM_SMALL_MEMORY_END   99 /*small memory systems end */
#define SYSTEM_LINUX              100
#define SYSTEM_LITEOS             120

#define TUYA_SL_0           0       /* security level 0,Applies to: Resource-constrained devices;Feature: Can only access Tuya Cloud */
#define TUYA_SL_1           1       /* security level 1,Applies to: Resource-constrained devices;Feature: One-way authentication */
#define TUYA_SL_2           2       /* security level 2,Applies to: Resource-rich equipment;Feature: Two-way authentication */
#define TUYA_SL_3           3       /* security level 3,Applies to: Resource-rich equipment;Feature: Two-way authentication,Devices use security chips to protect sensitive information */
// #define TUYA_MODIFY_MBEDTLS             1

#include "tuya_kconfig.h"

#ifndef TUYA_WEAK_ATTRIBUTE
#define TUYA_WEAK_ATTRIBUTE
#endif

/* custom settings */

// clang-format on

#ifdef __cplusplus
}
#endif

#endif
