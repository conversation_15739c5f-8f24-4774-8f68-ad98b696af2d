/*
 * 适配esp32c3 S3 H2芯片ADC，其它系列可能需要微调
 * 参考文档：
 * https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/api-reference/peripherals/adc.html
 * https://espressif-docs.readthedocs-hosted.com/projects/arduino-esp32/en/latest/api/adc.html
 * https://github.com/espressif/esp-idf/blob/v4.4/examples/peripherals/adc/dma_read/main/adc_dma_example_main.c
 */
#include <Arduino.h>
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_continuous.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp32_adc.h"

// 单次采样
#define NO_OF_SAMPLES 64           // 多次采样求平均，采样次数，用于平均，降低噪声
// 连续采样
#define TIMES 256
#define GET_UNIT(x) ((x >> 3) & 0x1)
#if CONFIG_IDF_TARGET_ESP32C3 || CONFIG_IDF_TARGET_ESP32H2
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_ALTER_UNIT // ESP32C3 only supports alter mode
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#elif CONFIG_IDF_TARGET_ESP32S3
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_BOTH_UNIT
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#endif

static uint8_t result[TIMES] = {0};
static esp_adc_cal_characteristics_t adc_chars;
static esp_adc_cal_value_t cal_type;

void adc_calibration(adc_unit_t unit, adc_atten_t atten, adc_bits_width_t width, uint16_t default_vref)
{
    cal_type = esp_adc_cal_characterize(unit, atten, width, default_vref, &adc_chars);
    if (cal_type == ESP_ADC_CAL_VAL_EFUSE_TP) {
        printf("ADC: Two Point values stored in eFuse\n");
    }
    else if (cal_type == ESP_ADC_CAL_VAL_EFUSE_TP_FIT) {
        printf("ADC: Two Point values and fitting curve coefficients stored in eFuse\n");
    }
    else if (cal_type == ESP_ADC_CAL_VAL_EFUSE_VREF) {
        printf("ADC: reference voltage stored in eFuse Vref = %dmV\n", adc_chars.vref);
    }
    else {
        printf("ADC: Default Vref = %dmV\n", default_vref);
    }
}

void adc_single_init(adc1_channel_t channel, adc_atten_t atten, adc_bits_width_t width)
{
    // 配置ADC单位和位宽
    adc1_config_width(width);
    // 配置ADC通道和衰减
    adc1_config_channel_atten(channel, atten);
}

void adc_continuous_init(uint16_t adc1_chan_mask, uint16_t adc2_chan_mask, adc_channel_t *channel, adc_atten_t atten)
{
    adc_digi_init_config_t adc_dma_config = {
        .max_store_buf_size = 1024,
        .conv_num_each_intr = TIMES,
        .adc1_chan_mask = adc1_chan_mask,
        .adc2_chan_mask = adc2_chan_mask,
    };
    ESP_ERROR_CHECK(adc_digi_initialize(&adc_dma_config));

    adc_digi_configuration_t dig_cfg = {
        .conv_limit_en = ADC_CONV_LIMIT_EN,
        .conv_limit_num = 250,
        .sample_freq_hz = 10 * 1000,
        .conv_mode = ADC_CONV_MODE,
        .format = ADC_OUTPUT_TYPE,
    };

    adc_digi_pattern_config_t adc_pattern[SOC_ADC_PATT_LEN_MAX] = {0};
    dig_cfg.pattern_num = 1;

    uint8_t unit = GET_UNIT(channel[0]);
    uint8_t ch = channel[0] & 0x7;
    adc_pattern[0].atten = atten;
    adc_pattern[0].channel = ch;
    adc_pattern[0].unit = unit;
    adc_pattern[0].bit_width = SOC_ADC_DIGI_MAX_BITWIDTH;

    printf("ADC: adc_pattern[%d].atten is :%x\n", 0, adc_pattern[0].atten);
    printf("ADC: adc_pattern[%d].channel is :%x\n", 0, adc_pattern[0].channel);
    printf("ADC: adc_pattern[%d].unit is :%x\n", 0, adc_pattern[0].unit);

    dig_cfg.adc_pattern = adc_pattern;
    ESP_ERROR_CHECK(adc_digi_controller_configure(&dig_cfg));
}

static bool check_valid_data(const adc_digi_output_data_t *data)
{
    const unsigned int unit = data->type2.unit;
    if (unit > 2)
        return false;
    if (data->type2.channel >= SOC_ADC_CHANNEL_NUM(unit))
        return false;

    return true;
}

float ReadVoltageContinuous(uint16_t adc1_chan_mask, uint16_t adc2_chan_mask)
{
    esp_err_t ret;
    uint32_t ret_num = 0;
    uint32_t voltage = 0, vol_num = 0, vol_total = 0;

    memset(result, 0xcc, TIMES);
    adc_digi_start();
    ret = adc_digi_read_bytes(result, TIMES, &ret_num, ADC_MAX_DELAY);
    if (ret == ESP_OK)
    {
        for (int i = 0; i < ret_num; i += ADC_RESULT_BYTE)
        {
            adc_digi_output_data_t *p = (adc_digi_output_data_t *)&result[i];
            // adc_digi_output_data_t *p = reinterpret_cast<adc_digi_output_data_t *>(&result[i]);
            if (ADC_CONV_MODE == ADC_CONV_BOTH_UNIT || ADC_CONV_MODE == ADC_CONV_ALTER_UNIT)
            {
                if (check_valid_data(p))
                {
                    vol_num++;
                    vol_total += p->type2.data;
                    vTaskDelay(pdMS_TO_TICKS(1));
                }
                else
                {
                    printf("ADC: Invalid data [%d_%d_%x]\n", p->type2.unit + 1, p->type2.channel, p->type2.data);
                }
            }
        }
        voltage = esp_adc_cal_raw_to_voltage(vol_total / vol_num, &adc_chars);
        vTaskDelay(pdMS_TO_TICKS(1));
    }
    else if (ret == ESP_ERR_TIMEOUT)
    {
        /**
         * ``ESP_ERR_TIMEOUT``: If ADC conversion is not finished until Timeout, you'll get this return error.
         * Here we set Timeout ``portMAX_DELAY``, so you'll never reach this branch.
         */
        printf("ADC: No data, increase timeout or reduce conv_num_each_intr");
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    adc_digi_stop();
    return (voltage * voltage_divider_factor / 1000.0);
}

float ReadVoltageSingle(uint8_t pin, adc1_channel_t channel, adc_atten_t atten, adc_bits_width_t width)
{
    float calibration = 1.000;
    uint32_t voltage, adc_reading = 0;

    if (cal_type != ESP_ADC_CAL_VAL_DEFAULT_VREF)
    {
        for (int i = 0; i < NO_OF_SAMPLES; i++)
        {
            adc_reading += adc1_get_raw(channel);
            vTaskDelay(pdMS_TO_TICKS(1));
        }
        adc_reading /= NO_OF_SAMPLES;
        voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_chars);
        return (voltage * voltage_divider_factor / 1000.0);
    }
    else
    {
        return (analogRead(pin) / 4095.0) * 3.3 * voltage_divider_factor * (1100 / default_vref) * calibration;
    }
}

