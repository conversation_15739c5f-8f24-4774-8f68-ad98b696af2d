From 18083a559734d297838e4cf34a856a4770062319 Mon Sep 17 00:00:00 2001
From: t<PERSON><PERSON><PERSON> <<EMAIL>>
Date: Tue, 23 Aug 2022 10:06:53 -0400
Subject: [PATCH] changes to compile with esp-idf

---
 CMakeLists.txt              | 11 ++++++-----
 src/vector/vimageloader.cpp | 23 +++++++++++++----------
 2 <USER> <GROUP>, 19 insertions(+), 15 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 38a9862..ee6d2cd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -95,10 +95,11 @@ if (NOT APPLE AND NOT WIN32)
                           )
 endif()
 
-if (LOTTIE_MODULE)
-    # for dlopen, dlsym and dlclose dependency
-    target_link_libraries(rlottie PRIVATE ${CMAKE_DL_LIBS})
-endif()
+# No Sym links in ESP-IDF
+# if (LOTTIE_MODULE)
+#     # for dlopen, dlsym and dlclose dependency
+#     target_link_libraries(rlottie PRIVATE ${CMAKE_DL_LIBS})
+# endif()
 
 if (NOT LOTTIE_ASAN)
     if(APPLE)
@@ -137,7 +138,7 @@ endif (NOT LIB_INSTALL_DIR)
 #declare source and include files
 add_subdirectory(inc)
 add_subdirectory(src)
-add_subdirectory(example)
+# add_subdirectory(example) // We dont need example dir in ESP-IDF
 
 if (LOTTIE_TEST)
     enable_testing()
diff --git a/src/vector/vimageloader.cpp b/src/vector/vimageloader.cpp
index c2446be..3df4c6a 100644
--- a/src/vector/vimageloader.cpp
+++ b/src/vector/vimageloader.cpp
@@ -6,7 +6,7 @@
 #ifdef _WIN32
 # include <windows.h>
 #else
-# include <dlfcn.h>
+// # include <dlfcn.h> //Does not work on ESP-IDF
 #endif  // _WIN32
 
 using lottie_image_load_f = unsigned char *(*)(const char *filename, int *x,
@@ -61,22 +61,25 @@ struct VImageLoader::Impl {
     void *dl_handle{nullptr};
     void  init()
     {
-        imageLoad = reinterpret_cast<lottie_image_load_f>(
-                    dlsym(dl_handle, "lottie_image_load"));
-        imageFree = reinterpret_cast<lottie_image_free_f>(
-                    dlsym(dl_handle, "lottie_image_free"));
-        imageFromData = reinterpret_cast<lottie_image_load_data_f>(
-                    dlsym(dl_handle, "lottie_image_load_from_data"));
+        // No sym links in ESP-iDF
+        // imageLoad = reinterpret_cast<lottie_image_load_f>(
+        //             dlsym(dl_handle, "lottie_image_load"));
+        // imageFree = reinterpret_cast<lottie_image_free_f>(
+        //             dlsym(dl_handle, "lottie_image_free"));
+        // imageFromData = reinterpret_cast<lottie_image_load_data_f>(
+        //             dlsym(dl_handle, "lottie_image_load_from_data"));
     }
 
     void moduleFree()
     {
-        if (dl_handle) dlclose(dl_handle);
+        // if (dl_handle) dlclose(dl_handle); // No sym links in ESP-iDF
     }
     bool moduleLoad()
     {
-        dl_handle = dlopen(LOTTIE_IMAGE_MODULE_PLUGIN, RTLD_LAZY);
-        return (dl_handle == nullptr);
+        // No sym links in ESP idf
+        // dl_handle = dlopen(LOTTIE_IMAGE_MODULE_PLUGIN, RTLD_LAZY);
+        // return (dl_handle == nullptr);
+        return true
     }
 # endif  // _WIN32
 #else  // LOTTIE_IMAGE_MODULE_SUPPORT
-- 
2.34.1

