##
# @file CMakeLists.txt
# @brief coreHTTP library CMakeLists
#/

# MODULE_PATH
set(MOD<PERSON><PERSON>_PATH ${CMAKE_CURRENT_SOURCE_DIR})

# MODULE_NAME
get_filename_component(MODULE_NAME ${MODULE_PATH} NAME)

# LIB_SRCS
file(GLOB_RECURSE LIB_SRCS 
    "${MODULE_PATH}/src/*.c"
)

# LIB_PUBLIC_INC
set(LIB_PUBLIC_INC
    ${MODULE_PATH}/include
    ${MODULE_PATH}/src
)

########################################
# Target Configure
########################################
idf_component_register(
    SRCS ${LIB_SRCS}
    INCLUDE_DIRS ${LIB_PUBLIC_INC}
    REQUIRES
        freertos
        lwip
        mbedtls
        tal_system
        tal_kv
        tuyaos_adapter
)

# Add compiler flags to suppress warnings for coreHTTP
target_compile_options(__idf_coreHTTP PRIVATE
    -Wno-error
    -Wno-unused-but-set-variable
    -Wno-discarded-qualifiers
    -Wno-pointer-sign
    -Wno-enum-compare
    -Wno-switch
    -Wno-implicit-fallthrough
)
