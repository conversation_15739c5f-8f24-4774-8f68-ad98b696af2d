/**
 * @file tuya_config_cli_simple.c
 * @brief Simplified Tuya configuration CLI using Arduino Serial
 * 
 * This is a backup/alternative implementation of the CLI system
 * that uses a simpler approach without complex UART handling.
 */

#include "tuya_config_cli.h"
#include "tuya_config_manager.h"
#include "tuya_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>
#include <stdio.h>

// ESP-IDF环境下的UART处理
#include "driver/uart.h"

// 使用printf系列函数，在ESP-IDF中更可靠
#define CLI_PRINT(x) printf("%s", x); fflush(stdout)
#define CLI_PRINTLN(x) printf("%s\n", x); fflush(stdout)
#define CLI_PRINTF(fmt, ...) printf(fmt, ##__VA_ARGS__); fflush(stdout)

// 简化的输入检测（在ESP-IDF环境下）
static int cli_uart_available(void) {
    size_t available = 0;
    esp_err_t ret = uart_get_buffered_data_len(UART_NUM_0, &available);
    return (ret == ESP_OK) ? (int)available : 0;
}

static int cli_uart_read(void) {
    uint8_t data;
    int len = uart_read_bytes(UART_NUM_0, &data, 1, pdMS_TO_TICKS(0));
    return (len > 0) ? data : -1;
}

#define CLI_AVAILABLE() cli_uart_available()
#define CLI_READ() cli_uart_read()

static const char *TAG = "TUYA_CONFIG_CLI_SIMPLE";

// CLI状态
static TaskHandle_t cli_task_handle = NULL;
static bool cli_initialized = false;
static char cli_command_buffer[256];
static int cli_buffer_pos = 0;

// 前向声明
static void cmd_tuya_config_show_simple(void);
static void cmd_tuya_config_set_uuid_simple(const char *uuid);
static void cmd_tuya_config_set_authkey_simple(const char *authkey);
static void cmd_tuya_config_reset_simple(void);
static void cmd_tuya_config_save_current_simple(void);

// CLI任务函数 - 简化版本
static void cli_task_simple(void *pvParameters)
{
    CLI_PRINTLN("\n=== Tuya Configuration CLI (Simple Version) ===");
    CLI_PRINTLN("Type 'help' for available commands");
    CLI_PRINT("tuya-simple> ");
    
    while (1) {
        if (CLI_AVAILABLE() > 0) {
            char ch = CLI_READ();
            
            if (ch == '\r' || ch == '\n') {
                // 回车键 - 处理命令
                if (cli_buffer_pos > 0) {
                    cli_command_buffer[cli_buffer_pos] = '\0';
                    CLI_PRINTLN("");
                    
                    // 处理命令
                    tuya_config_cli_process_command_simple(cli_command_buffer);
                    
                    // 重置缓冲区
                    cli_buffer_pos = 0;
                    memset(cli_command_buffer, 0, sizeof(cli_command_buffer));
                }
                CLI_PRINT("tuya-simple> ");
            }
            else if (ch == '\b' || ch == 127) {
                // 退格键
                if (cli_buffer_pos > 0) {
                    cli_buffer_pos--;
                    cli_command_buffer[cli_buffer_pos] = '\0';
                    CLI_PRINT("\b \b");
                }
            }
            else if (ch >= 32 && ch <= 126) {
                // 可打印字符
                if (cli_buffer_pos < sizeof(cli_command_buffer) - 1) {
                    cli_command_buffer[cli_buffer_pos] = ch;
                    cli_buffer_pos++;
                    CLI_PRINTF("%c", ch);
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(50)); // 50ms延迟，更节省CPU
    }
}

// CLI命令处理函数 - 简化版本
static void cmd_tuya_config_show_simple(void)
{
    CLI_PRINTLN("=== Current Tuya Configuration (Simple) ===");
    const tuya_config_data_t *config = tuya_config_get();
    CLI_PRINTF("Product Key: %s\n", config->product_key);
    CLI_PRINTF("Device UUID: %s\n", config->device_uuid);
    CLI_PRINTF("Device AuthKey: %s\n", config->device_authkey);
    CLI_PRINTF("Software Ver: %s\n", config->software_ver);
    CLI_PRINTLN("==========================================");
}

static void cmd_tuya_config_set_uuid_simple(const char *uuid)
{
    if (!uuid || strlen(uuid) == 0) {
        CLI_PRINTLN("Error: Invalid UUID parameter");
        return;
    }
    
    int ret = tuya_config_update_uuid(uuid);
    if (ret == OPRT_OK) {
        CLI_PRINTF("UUID updated successfully: %s\n", uuid);
    } else {
        CLI_PRINTF("Failed to update UUID: %d\n", ret);
    }
}

static void cmd_tuya_config_set_authkey_simple(const char *authkey)
{
    if (!authkey || strlen(authkey) == 0) {
        CLI_PRINTLN("Error: Invalid AuthKey parameter");
        return;
    }
    
    int ret = tuya_config_update_authkey(authkey);
    if (ret == OPRT_OK) {
        CLI_PRINTF("AuthKey updated successfully: %s\n", authkey);
    } else {
        CLI_PRINTF("Failed to update AuthKey: %d\n", ret);
    }
}

static void cmd_tuya_config_reset_simple(void)
{
    CLI_PRINTLN("Resetting Tuya configuration to defaults...");
    int ret = tuya_config_reset_to_defaults();
    if (ret == OPRT_OK) {
        CLI_PRINTLN("Configuration reset successfully");
        cmd_tuya_config_show_simple();
    } else {
        CLI_PRINTF("Failed to reset configuration: %d\n", ret);
    }
}

static void cmd_tuya_config_save_current_simple(void)
{
    const tuya_config_data_t *config = tuya_config_get();
    int ret = tuya_config_save(config);
    if (ret == OPRT_OK) {
        CLI_PRINTLN("Configuration saved successfully");
    } else {
        CLI_PRINTF("Failed to save configuration: %d\n", ret);
    }
}

// 简单的命令解析器
void tuya_config_cli_process_command_simple(const char *command)
{
    if (!command || strlen(command) == 0) {
        return;
    }
    
    TY_LOGI("Processing CLI command (simple): %s", command);
    
    // 显示配置
    if (strcmp(command, "show") == 0) {
        cmd_tuya_config_show_simple();
    }
    // 重置配置
    else if (strcmp(command, "reset") == 0) {
        cmd_tuya_config_reset_simple();
    }
    // 保存配置
    else if (strcmp(command, "save") == 0) {
        cmd_tuya_config_save_current_simple();
    }
    // 设置UUID
    else if (strncmp(command, "set uuid ", 9) == 0) {
        const char *uuid = command + 9;
        cmd_tuya_config_set_uuid_simple(uuid);
    }
    // 设置AuthKey
    else if (strncmp(command, "set authkey ", 12) == 0) {
        const char *authkey = command + 12;
        cmd_tuya_config_set_authkey_simple(authkey);
    }
    // 帮助信息
    else if (strcmp(command, "help") == 0) {
        tuya_config_cli_print_help_simple();
    }
    // 测试配置
    else if (strcmp(command, "test") == 0) {
        tuya_config_cli_set_test_config_simple();
    }
    else {
        CLI_PRINTF("Unknown command: %s\n", command);
        CLI_PRINTLN("Type 'help' for available commands");
    }
}

void tuya_config_cli_print_help_simple(void)
{
    CLI_PRINTLN("=== Tuya Configuration CLI Commands (Simple) ===");
    CLI_PRINTLN("show                          - Show current configuration");
    CLI_PRINTLN("set uuid <uuid>               - Set device UUID");
    CLI_PRINTLN("set authkey <authkey>         - Set device AuthKey");
    CLI_PRINTLN("save                          - Save current config to NVS");
    CLI_PRINTLN("reset                         - Reset to default values");
    CLI_PRINTLN("test                          - Set test configuration");
    CLI_PRINTLN("help                          - Show this help");
    CLI_PRINTLN("===============================================");
    CLI_PRINTLN("");
    CLI_PRINTLN("Examples:");
    CLI_PRINTLN("  set uuid tuyac0828c3dfbc6a170");
    CLI_PRINTLN("  set authkey gxQPs3qtR0pNSx12hh0floVG117uUnJL");
    CLI_PRINTLN("  show");
    CLI_PRINTLN("  save");
}

// 预设配置函数
void tuya_config_cli_set_test_config_simple(void)
{
    CLI_PRINTLN("Setting test configuration (simple)...");
    
    tuya_config_data_t test_config = {
        .product_key = "ff1lwoe4t5rkeg5m",
        .device_uuid = "tuyac0828c3dfbc6a170",
        .device_authkey = "gxQPs3qtR0pNSx12hh0floVG117uUnJL",
        .software_ver = "1.0.0"
    };
    
    int ret = tuya_config_save(&test_config);
    if (ret == OPRT_OK) {
        CLI_PRINTLN("Test configuration saved successfully");
        cmd_tuya_config_show_simple();
    } else {
        CLI_PRINTF("Failed to save test configuration: %d\n", ret);
    }
}

void tuya_config_cli_init_simple(void)
{
    if (cli_initialized) {
        TY_LOGW("CLI (simple) already initialized");
        return;
    }
    
    TY_LOGI("Initializing Tuya Configuration CLI (Simple Version)...");
    
    // 创建CLI任务
    BaseType_t task_ret = xTaskCreate(cli_task_simple, "cli_simple", 3072, NULL, 4, &cli_task_handle);
    if (task_ret != pdPASS) {
        TY_LOGE("Failed to create CLI simple task");
        return;
    }
    
    cli_initialized = true;
    TY_LOGI("Tuya Configuration CLI (Simple) initialized successfully");
    
    // 延迟一下让任务启动
    vTaskDelay(pdMS_TO_TICKS(100));
}

void tuya_config_cli_deinit_simple(void)
{
    if (!cli_initialized) {
        return;
    }
    
    TY_LOGI("Deinitializing CLI (simple)...");
    
    // 删除任务
    if (cli_task_handle) {
        vTaskDelete(cli_task_handle);
        cli_task_handle = NULL;
    }
    
    cli_initialized = false;
    TY_LOGI("CLI (simple) deinitialized");
}

bool tuya_config_cli_is_initialized_simple(void)
{
    return cli_initialized;
}
