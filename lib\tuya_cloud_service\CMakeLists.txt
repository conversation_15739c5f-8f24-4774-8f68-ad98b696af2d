##
# @file CMakeLists.txt
# @brief Tuya Cloud Service component for ESP-IDF
#/

# Get base source files
file(GLOB_RECURSE component_srcs
    "${CMAKE_CURRENT_SOURCE_DIR}/lan/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/netmgr/netmgr.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/protocol/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/schema/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/cloud/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/tls/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/transport/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/authorize/*.c"
)

# Set base include directories
set(component_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/cloud"
    "${CMAKE_CURRENT_SOURCE_DIR}/protocol"
    "${CMAKE_CURRENT_SOURCE_DIR}/schema"
    "${CMAKE_CURRENT_SOURCE_DIR}/lan"
    "${CMAKE_CURRENT_SOURCE_DIR}/tls"
    "${CMAKE_CURRENT_SOURCE_DIR}/netmgr"
    "${CMAKE_CURRENT_SOURCE_DIR}/transport"
    "${CMAKE_CURRENT_SOURCE_DIR}/authorize"
)

# Add WiFi sources if enabled
if(CONFIG_ENABLE_WIFI)
    file(GLOB_RECURSE wifi_srcs
        "${CMAKE_CURRENT_SOURCE_DIR}/netmgr/netconn_wifi.c"
        "${CMAKE_CURRENT_SOURCE_DIR}/netcfg/*.c"
    )
    list(APPEND component_srcs ${wifi_srcs})
    list(APPEND component_includes "${CMAKE_CURRENT_SOURCE_DIR}/netcfg")
endif()

# Add Wired sources if enabled
if(CONFIG_ENABLE_WIRED)
    file(GLOB_RECURSE wired_srcs
        "${CMAKE_CURRENT_SOURCE_DIR}/netmgr/netconn_wired.c"
    )
    list(APPEND component_srcs ${wired_srcs})
endif()

# Add Bluetooth sources if enabled
if(CONFIG_ENABLE_BLUETOOTH)
    file(GLOB_RECURSE ble_srcs
        "${CMAKE_CURRENT_SOURCE_DIR}/ble/*.c"
    )
    list(APPEND component_srcs ${ble_srcs})
    list(APPEND component_includes "${CMAKE_CURRENT_SOURCE_DIR}/ble")
endif()

# Register ESP-IDF component
idf_component_register(
    SRCS ${component_srcs}
    INCLUDE_DIRS ${component_includes}
    REQUIRES
        freertos
        esp_netif
        esp_event
        esp_wifi
        lwip
        mbedtls
        nvs_flash
        common
        tal_system
        tal_network
        tal_kv
)

