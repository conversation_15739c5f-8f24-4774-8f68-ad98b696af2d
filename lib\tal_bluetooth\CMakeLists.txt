##
# @file CMakeLists.txt
# @brief TAL Bluetooth component for ESP-IDF
#/

# Get base source files
file(GLOB_RECURSE component_srcs
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.c"
)

# Set base include directories
set(component_includes
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

set(component_priv_includes "")

# Add NimBLE sources if enabled
if(CONFIG_ENABLE_NIMBLE)
    file(GLOB_RECURSE nimble_srcs
        "${CMAKE_CURRENT_SOURCE_DIR}/nimble/*.c"
    )
    list(APPEND component_srcs ${nimble_srcs})
    list(APPEND component_includes "${CMAKE_CURRENT_SOURCE_DIR}/nimble/include")
    list(APPEND component_priv_includes "${CMAKE_CURRENT_SOURCE_DIR}/nimble/host")
endif()

# Register ESP-IDF component
idf_component_register(
    SRCS ${component_srcs}
    INCLUDE_DIRS ${component_includes}
    PRIV_INCLUDE_DIRS ${component_priv_includes}
    REQUIRES
        freertos
        bt
        tal_system
)

