#include <OneButton.h>

#define BUTTON1_PIN 6  // 第一个按钮连接 GPIO6
#define BUTTON2_PIN 7  // 第二个按钮连接 GPIO7

// 创建两个 OneButton 实例（低电平触发，接 GND）
OneButton button1(BUTTON1_PIN, true);
OneButton button2(BUTTON2_PIN, true);

// 公共的长按动作回调（任意一个按钮按下超过3秒就会触发）
void onLongPress3s() {
  Serial.println("任意一个按钮长按 >= 3 秒，执行动作！");
  // 在这里执行你的操作，例如蓝牙配网、进入休眠等
}

void setup() {
  Serial.begin(115200);

  // 设置长按判定阈值为 3 秒
  button1.setPressTicks(3000);
  button2.setPressTicks(3000);

  // 注册长按事件（开始时触发）
  button1.attachLongPressStart(onLongPress3s);
  button2.attachLongPressStart(onLongPress3s);
}

void loop() {
  // 必须轮询两个按钮的 tick()
  button1.tick();
  button2.tick();

  delay(10); // 不能太长，避免漏检测
}