/**
 * @file tuya_debug.h
 * @brief Tuya IoT debugging utilities header
 */

#ifndef TUYA_DEBUG_H
#define TUYA_DEBUG_H

#include "tuya_iot.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Print Tuya IoT configuration details
 * @param config Tuya IoT configuration structure
 */
void tuya_debug_print_config(const tuya_iot_config_t *config);

/**
 * @brief Print configuration defines from tuya_config.h
 */
void tuya_debug_print_defines(void);

/**
 * @brief Analyze and print storage key generation logic
 * @param config Tuya IoT configuration structure
 */
void tuya_debug_analyze_storage_keys(const tuya_iot_config_t *config);

/**
 * @brief Check if activation data exists in storage
 * @param config Tuya IoT configuration structure
 */
void tuya_debug_check_activation_data(const tuya_iot_config_t *config);

/**
 * @brief Perform full debug analysis
 * @param config Tuya IoT configuration structure
 */
void tuya_debug_full_analysis(const tuya_iot_config_t *config);

#ifdef __cplusplus
}
#endif

#endif /* TUYA_DEBUG_H */
