/**
 * @file led_button_control.h
 * @brief ESP32-C3 LED和按键控制系统
 * 
 * 功能说明：
 * 1. WS2812B LED控制（白、红、绿、蓝四种颜色）
 * 2. GPIO6/GPIO7按键控制
 * 3. GPIO5/GPIO1输出控制
 * 4. 呼吸灯效果
 * 5. 工厂重置功能
 */

#ifndef LED_BUTTON_CONTROL_H
#define LED_BUTTON_CONTROL_H

#include <Arduino.h>
#include <FastLED.h>
#include <OneButton.h>

// GPIO定义
#define LED_PIN         GPIO_NUM_8    // WS2812B LED引脚
#define BUTTON_GPIO6    GPIO_NUM_6    // 按键6
#define BUTTON_GPIO7    GPIO_NUM_7    // 按键7
#define OUTPUT_GPIO5    GPIO_NUM_5    // 输出引脚5
#define OUTPUT_GPIO1    GPIO_NUM_1    // 输出引脚1

// LED配置
#define NUM_LEDS        1             // LED数量
#define LED_TYPE        WS2812B       // LED类型
#define COLOR_ORDER     GRB           // 颜色顺序

// 时间配置
#define FACTORY_RESET_TIME  10000     // 工厂重置长按时间（10秒）
#define BREATHING_SPEED     20        // 呼吸灯速度

// LED颜色定义
typedef enum {
    LED_COLOR_OFF = 0,
    LED_COLOR_WHITE,
    LED_COLOR_RED,
    LED_COLOR_GREEN,
    LED_COLOR_BLUE
} led_color_t;

// 设备状态定义
typedef enum {
    DEVICE_STATE_UNCONFIGURED = 0,    // 未配网
    DEVICE_STATE_CONFIGURED,          // 已配网
    DEVICE_STATE_GPIO5_ACTIVE,        // GPIO5激活状态
    DEVICE_STATE_GPIO5_GPIO1_ACTIVE   // GPIO5和GPIO1都激活状态
} device_state_t;

// GPIO状态定义
typedef struct {
    bool gpio5_active;
    bool gpio1_active;
} gpio_state_t;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化LED和按键控制系统
 */
void led_button_init(void);

/**
 * @brief LED控制任务（需要在loop中调用）
 */
void led_button_task(void);

/**
 * @brief 设置LED颜色
 * @param color LED颜色
 * @param breathing 是否启用呼吸效果
 */
void set_led_color(led_color_t color, bool breathing);

/**
 * @brief 设置设备状态（影响LED显示）
 * @param state 设备状态
 */
void set_device_state(device_state_t state);

/**
 * @brief 获取当前设备状态
 * @return 当前设备状态
 */
device_state_t get_device_state(void);

/**
 * @brief 获取GPIO状态
 * @return GPIO状态结构体
 */
gpio_state_t get_gpio_state(void);

/**
 * @brief 设置GPIO5状态
 * @param active true为高电平，false为低电平
 */
void set_gpio5_state(bool active);

/**
 * @brief 设置GPIO1状态
 * @param active true为高电平，false为低电平
 */
void set_gpio1_state(bool active);

/**
 * @brief 切换GPIO5状态
 */
void toggle_gpio5_state(void);

/**
 * @brief 切换GPIO5和GPIO1状态
 */
void toggle_gpio5_gpio1_state(void);

/**
 * @brief 执行工厂重置
 */
void perform_factory_reset(void);

/**
 * @brief 检查是否需要工厂重置
 * @return true如果需要工厂重置
 */
bool is_factory_reset_requested(void);

/**
 * @brief 重置工厂重置请求
 */
void reset_factory_reset_request(void);

#ifdef __cplusplus
}
#endif

#endif /* LED_BUTTON_CONTROL_H */
